import os
import json
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, 
                            QCheckBox, QLineEdit, QPushButton, QComboBox, QTabWidget,
                            QScrollArea, QGroupBox, QFormLayout, QSpinBox, QFileDialog,
                            QRadioButton, QProgressBar, QTextEdit)
from PyQt5.QtGui import QColor, QPixmap, QIcon, QFont
from PyQt5.QtCore import Qt, QTimer

class BuilderWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
        # For simulation purposes
        self.build_progress = 0
        self.build_timer = QTimer()
        self.build_timer.timeout.connect(self.update_build_progress)
    
    def setup_ui(self):
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Add title with educational disclaimer
        title_layout = QHBoxLayout()
        
        title_label = QLabel("Builder")
        title_label.setStyleSheet("color: #FFFFFF; font-size: 24px; font-weight: bold;")
        
        disclaimer_label = QLabel("(Educational Simulation)")
        disclaimer_label.setStyleSheet("color: #FF5555; font-size: 16px; font-style: italic;")
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(disclaimer_label)
        title_layout.addStretch()
        
        main_layout.addLayout(title_layout)
        
        # Add educational notice
        notice_frame = QFrame()
        notice_frame.setStyleSheet("background-color: #2D2D3F; border-radius: 5px;")
        notice_layout = QVBoxLayout(notice_frame)
        
        notice_title = QLabel("⚠️ Educational Notice")
        notice_title.setStyleSheet("color: #FFAA00; font-size: 16px; font-weight: bold;")
        
        notice_text = QLabel(
            "This is a simulated interface for educational purposes only. "
            "It demonstrates how malware builder interfaces might look, to help understand "
            "and protect against such threats. No actual harmful code is generated."
        )
        notice_text.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        notice_text.setWordWrap(True)
        
        notice_layout.addWidget(notice_title)
        notice_layout.addWidget(notice_text)
        
        main_layout.addWidget(notice_frame)
        
        # Create main content area with two columns
        content_layout = QHBoxLayout()
        
        # Left column - Build options
        left_column = QVBoxLayout()
        
        # Build options
        options_frame = QFrame()
        options_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        options_layout = QVBoxLayout(options_frame)
        
        options_title = QLabel("Build Options")
        options_title.setStyleSheet("color: #FFFFFF; font-size: 16px; font-weight: bold;")
        options_layout.addWidget(options_title)
        
        # Build type
        build_type_group = QGroupBox("Build Type")
        build_type_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        build_type_layout = QVBoxLayout(build_type_group)
        
        # Radio button style
        radio_style = """
            QRadioButton {
                color: #FFFFFF;
                font-size: 14px;
                spacing: 10px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                background-color: #2D2D3F;
            }
            QRadioButton::indicator:checked {
                background-color: #8E79E8;
                border: 4px solid #2D2D3F;
            }
            QRadioButton::indicator:unchecked:hover {
                background-color: #3D3D4F;
            }
        """
        
        # Build type options
        build_types = [
            {"name": "Windows Executable (.exe)", "checked": True},
            {"name": "Windows DLL (.dll)", "checked": False},
            {"name": "PowerShell Script (.ps1)", "checked": False},
            {"name": "Python Script (.py)", "checked": False},
            {"name": "JavaScript (.js)", "checked": False}
        ]
        
        for build_type in build_types:
            radio = QRadioButton(build_type["name"])
            radio.setStyleSheet(radio_style)
            radio.setChecked(build_type["checked"])
            build_type_layout.addWidget(radio)
        
        options_layout.addWidget(build_type_group)
        
        # Payload options
        payload_group = QGroupBox("Payload Options")
        payload_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        payload_layout = QVBoxLayout(payload_group)
        
        # Checkbox style
        checkbox_style = """
            QCheckBox {
                color: #FFFFFF;
                font-size: 14px;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                background-color: #2D2D3F;
            }
            QCheckBox::indicator:checked {
                background-color: #8E79E8;
                image: url('checkmark.png');
            }
            QCheckBox::indicator:unchecked:hover {
                background-color: #3D3D4F;
            }
        """
        
        # Payload options
        payload_options = [
            {"name": "Use Settings from Stealer Settings", "checked": True},
            {"name": "Encrypt Payload", "checked": True},
            {"name": "Use Anti-Detection Techniques", "checked": True},
            {"name": "Add Persistence", "checked": True},
            {"name": "Self Destruct After Execution", "checked": False}
        ]
        
        for option in payload_options:
            checkbox = QCheckBox(option["name"])
            checkbox.setStyleSheet(checkbox_style)
            checkbox.setChecked(option["checked"])
            payload_layout.addWidget(checkbox)
        
        options_layout.addWidget(payload_group)
        
        # Icon selection
        icon_group = QGroupBox("Icon Selection")
        icon_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        icon_layout = QVBoxLayout(icon_group)
        
        # Icon options
        icon_options = [
            {"name": "Default Icon", "checked": True},
            {"name": "Chrome Icon", "checked": False},
            {"name": "PDF Icon", "checked": False},
            {"name": "Word Icon", "checked": False},
            {"name": "Custom Icon", "checked": False}
        ]
        
        for option in icon_options:
            radio = QRadioButton(option["name"])
            radio.setStyleSheet(radio_style)
            radio.setChecked(option["checked"])
            icon_layout.addWidget(radio)
        
        # Custom icon selection
        custom_icon_layout = QHBoxLayout()
        
        custom_icon_path = QLineEdit()
        custom_icon_path.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        custom_icon_path.setPlaceholderText("Select custom icon path...")
        custom_icon_path.setEnabled(False)
        
        browse_button = QPushButton("Browse")
        browse_button.setStyleSheet("""
            QPushButton {
                background-color: #3D3D4F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #4D4D5F;
            }
            QPushButton:disabled {
                background-color: #2D2D3F;
                color: #888888;
            }
        """)
        browse_button.setEnabled(False)
        browse_button.clicked.connect(self.browse_icon)
        
        custom_icon_layout.addWidget(custom_icon_path)
        custom_icon_layout.addWidget(browse_button)
        
        icon_layout.addLayout(custom_icon_layout)
        
        options_layout.addWidget(icon_group)
        
        left_column.addWidget(options_frame)
        
        # Right column - Assembly options and build button
        right_column = QVBoxLayout()
        
        # Assembly options
        assembly_frame = QFrame()
        assembly_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        assembly_layout = QVBoxLayout(assembly_frame)
        
        assembly_title = QLabel("Assembly Information")
        assembly_title.setStyleSheet("color: #FFFFFF; font-size: 16px; font-weight: bold;")
        assembly_layout.addWidget(assembly_title)
        
        # Form style
        form_style = """
            QLabel {
                color: #FFFFFF;
                font-size: 14px;
            }
            QLineEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """
        
        # Assembly form
        form_widget = QWidget()
        form_widget.setStyleSheet(form_style)
        form_layout = QFormLayout(form_widget)
        form_layout.setSpacing(15)
        
        # Assembly name
        assembly_name = QLineEdit()
        assembly_name.setText("WindowsUpdate")
        form_layout.addRow("Assembly Name:", assembly_name)
        
        # Assembly description
        assembly_description = QLineEdit()
        assembly_description.setText("Windows Update Service")
        form_layout.addRow("Description:", assembly_description)
        
        # Assembly company
        assembly_company = QLineEdit()
        assembly_company.setText("Microsoft Corporation")
        form_layout.addRow("Company:", assembly_company)
        
        # Assembly product
        assembly_product = QLineEdit()
        assembly_product.setText("Windows Operating System")
        form_layout.addRow("Product:", assembly_product)
        
        # Assembly copyright
        assembly_copyright = QLineEdit()
        assembly_copyright.setText("© Microsoft Corporation. All rights reserved.")
        form_layout.addRow("Copyright:", assembly_copyright)
        
        # Assembly version
        assembly_version = QLineEdit()
        assembly_version.setText("10.0.19041.1")
        form_layout.addRow("Version:", assembly_version)
        
        assembly_layout.addWidget(form_widget)
        
        right_column.addWidget(assembly_frame)
        
        # Output options
        output_frame = QFrame()
        output_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        output_layout = QVBoxLayout(output_frame)
        
        output_title = QLabel("Output Options")
        output_title.setStyleSheet("color: #FFFFFF; font-size: 16px; font-weight: bold;")
        output_layout.addWidget(output_title)
        
        # Output path
        output_path_layout = QHBoxLayout()
        
        output_path = QLineEdit()
        output_path.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        output_path.setText(os.path.expanduser("~/Desktop/output.exe"))
        
        output_browse = QPushButton("Browse")
        output_browse.setStyleSheet("""
            QPushButton {
                background-color: #3D3D4F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #4D4D5F;
            }
        """)
        output_browse.clicked.connect(self.browse_output)
        
        output_path_layout.addWidget(output_path)
        output_path_layout.addWidget(output_browse)
        
        output_layout.addLayout(output_path_layout)
        
        right_column.addWidget(output_frame)
        
        # Build button and progress
        build_frame = QFrame()
        build_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        build_layout = QVBoxLayout(build_frame)
        
        # Build button
        build_button = QPushButton("Build (Educational Simulation)")
        build_button.setStyleSheet("""
            QPushButton {
                background-color: #8E79E8;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 12px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7A67C7;
            }
        """)
        build_button.clicked.connect(self.start_build_simulation)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #8E79E8;
                border-radius: 5px;
            }
        """)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        
        # Build log
        log_label = QLabel("Build Log (Educational Simulation):")
        log_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        
        self.build_log = QTextEdit()
        self.build_log.setStyleSheet("""
            QTextEdit {
                background-color: #2D2D3F;
                color: #AAAAAA;
                border: none;
                border-radius: 5px;
                font-family: monospace;
                font-size: 12px;
            }
        """)
        self.build_log.setReadOnly(True)
        self.build_log.setFixedHeight(150)
        
        build_layout.addWidget(build_button)
        build_layout.addWidget(self.progress_bar)
        build_layout.addWidget(log_label)
        build_layout.addWidget(self.build_log)
        
        right_column.addWidget(build_frame)
        
        # Add columns to content layout
        content_layout.addLayout(left_column)
        content_layout.addLayout(right_column)
        
        main_layout.addLayout(content_layout)
        
        # Add educational information at the bottom
        info_frame = QFrame()
        info_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        info_layout = QVBoxLayout(info_frame)
        
        info_title = QLabel("🔒 Security Information")
        info_title.setStyleSheet("color: #8E79E8; font-size: 16px; font-weight: bold;")
        
        info_text = QLabel(
            "Understanding how malware is created helps in developing better defenses. To protect yourself:\n"
            "• Keep your operating system and applications updated\n"
            "• Use reputable antivirus and anti-malware software\n"
            "• Be cautious about downloading files from untrusted sources\n"
            "• Verify the authenticity of software before installation\n"
            "• Use application whitelisting where appropriate\n"
            "• Implement proper network security measures"
        )
        info_text.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        info_text.setWordWrap(True)
        
        info_layout.addWidget(info_title)
        info_layout.addWidget(info_text)
        
        main_layout.addWidget(info_frame)
    
    def browse_icon(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Icon", "", "Icon Files (*.ico *.png);;All Files (*)"
        )
        if file_path:
            # In a real application, this would set the icon path
            print(f"Selected icon: {file_path}")
    
    def browse_output(self):
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Output", "", "Executable Files (*.exe);;All Files (*)"
        )
        if file_path:
            # In a real application, this would set the output path
            print(f"Selected output path: {file_path}")
    
    def start_build_simulation(self):
        # Reset progress
        self.build_progress = 0
        self.progress_bar.setValue(0)
        self.build_log.clear()
        
        # Add initial log message
        self.add_log_message("[*] Starting educational build simulation...")
        self.add_log_message("[*] This is a simulated build process for educational purposes only.")
        self.add_log_message("[*] No actual malware is being created.")
        
        # Start the timer for simulated build progress
        self.build_timer.start(100)
    
    def update_build_progress(self):
        self.build_progress += 1
        self.progress_bar.setValue(self.build_progress)
        
        # Add log messages at specific points
        if self.build_progress == 10:
            self.add_log_message("[+] Initializing build environment...")
        elif self.build_progress == 20:
            self.add_log_message("[+] Loading configuration from Stealer Settings...")
        elif self.build_progress == 30:
            self.add_log_message("[+] Preparing assembly information...")
        elif self.build_progress == 40:
            self.add_log_message("[+] Generating simulated code structure...")
        elif self.build_progress == 50:
            self.add_log_message("[+] Applying anti-detection techniques (simulation)...")
        elif self.build_progress == 60:
            self.add_log_message("[+] Adding encryption layers (simulation)...")
        elif self.build_progress == 70:
            self.add_log_message("[+] Implementing persistence mechanisms (simulation)...")
        elif self.build_progress == 80:
            self.add_log_message("[+] Setting up icon and resources...")
        elif self.build_progress == 90:
            self.add_log_message("[+] Compiling final executable (simulation)...")
        elif self.build_progress == 100:
            self.add_log_message("[+] Build completed successfully (educational simulation only)")
            self.add_log_message("[*] REMINDER: This is only a simulation for educational purposes.")
            self.build_timer.stop()
            
            # Show a message in the parent window's status bar if available
            if hasattr(self.parent(), "statusBar"):
                self.parent().statusBar().showMessage("Build simulation completed (Educational Purposes Only)", 5000)
    
    def add_log_message(self, message):
        self.build_log.append(message)
        # Auto-scroll to the bottom
        scrollbar = self.build_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
