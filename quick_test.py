#!/usr/bin/env python3
"""
Quick test script to verify basic functionality
"""

import sys
import os

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test if all modules can be imported"""
    print("Testing module imports...")
    
    modules_to_test = [
        ('database', 'DatabaseManager'),
        ('system_monitor', 'SystemMonitor'),
        ('password_manager', 'PasswordGenerator'),
        ('config', 'Config'),
    ]
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name)
            if hasattr(module, class_name):
                print(f"✓ {module_name}.{class_name}")
            else:
                print(f"✗ {module_name}.{class_name} not found")
                return False
        except ImportError as e:
            print(f"✗ Failed to import {module_name}: {e}")
            return False
    
    return True

def test_database():
    """Test database functionality"""
    print("\nTesting database...")
    
    try:
        from database import DatabaseManager
        
        # Create temporary database
        db = DatabaseManager("test_temp.db")
        
        # Test basic operations
        db.insert_system_monitoring(50.0, 60.0, 70.0, 1000, 2000, 100)
        data = db.get_recent_system_data(1)
        
        if len(data) > 0:
            print("✓ Database operations working")
        else:
            print("✗ Database operations failed")
            return False
        
        # Clean up
        if os.path.exists("test_temp.db"):
            os.remove("test_temp.db")
        
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def test_system_monitor():
    """Test system monitoring"""
    print("\nTesting system monitor...")
    
    try:
        from system_monitor import SystemMonitor
        
        monitor = SystemMonitor()
        data = monitor.collect_system_data()
        
        if data and 'cpu_percent' in data:
            print("✓ System monitoring working")
            print(f"  CPU: {data['cpu_percent']:.1f}%")
            print(f"  Memory: {data['memory_percent']:.1f}%")
            return True
        else:
            print("✗ System monitoring failed")
            return False
            
    except Exception as e:
        print(f"✗ System monitor test failed: {e}")
        return False

def test_password_generator():
    """Test password generation"""
    print("\nTesting password generator...")
    
    try:
        from password_manager import PasswordGenerator
        
        password = PasswordGenerator.generate_password(12)
        strength = PasswordGenerator.check_password_strength(password)
        
        if len(password) == 12 and strength['score'] > 0:
            print("✓ Password generation working")
            print(f"  Generated password length: {len(password)}")
            print(f"  Password strength: {strength['strength']}")
            return True
        else:
            print("✗ Password generation failed")
            return False
            
    except Exception as e:
        print(f"✗ Password generator test failed: {e}")
        return False

def main():
    """Run quick tests"""
    print("Cybersecurity Monitoring System - Quick Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_database,
        test_system_monitor,
        test_password_generator
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! The application should work correctly.")
        print("\nYou can now run the full application using:")
        print("  python run_app.py")
        return 0
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
