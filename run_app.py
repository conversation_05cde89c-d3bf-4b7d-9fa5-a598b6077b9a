#!/usr/bin/env python3
"""
Cybersecurity Monitoring System
Real-time cybersecurity monitoring and management platform

This script launches the main application with proper error handling and logging.
"""

import sys
import os
import logging
import traceback
from datetime import datetime

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def setup_logging():
    """Set up logging configuration"""
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_filename = os.path.join(log_dir, f"cybersecurity_app_{datetime.now().strftime('%Y%m%d')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'PyQt5',
        'psutil',
        'cryptography',
        'matplotlib',
        'pandas',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nPlease install missing packages using:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_system_requirements():
    """Check system requirements"""
    import platform
    
    logger = logging.getLogger(__name__)
    
    # Check Python version
    python_version = sys.version_info
    if python_version < (3, 8):
        logger.error(f"Python 3.8+ required, found {python_version.major}.{python_version.minor}")
        return False
    
    # Check operating system
    os_name = platform.system()
    logger.info(f"Operating System: {os_name} {platform.release()}")
    
    # Check available memory
    try:
        import psutil
        memory = psutil.virtual_memory()
        if memory.total < 2 * 1024 * 1024 * 1024:  # 2GB
            logger.warning("Low system memory detected. Application may run slowly.")
    except ImportError:
        pass
    
    return True

def create_data_directories():
    """Create necessary data directories"""
    directories = ['data', 'logs', 'reports', 'backups']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")

def main():
    """Main application entry point"""
    logger = setup_logging()
    logger.info("Starting Cybersecurity Monitoring System")
    
    try:
        # Check dependencies
        if not check_dependencies():
            logger.error("Dependency check failed")
            sys.exit(1)
        
        # Check system requirements
        if not check_system_requirements():
            logger.error("System requirements check failed")
            sys.exit(1)
        
        # Create necessary directories
        create_data_directories()
        
        # Import PyQt5 after dependency check
        from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
        from PyQt5.QtGui import QPixmap, QFont
        from PyQt5.QtCore import Qt, QTimer
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("Cybersecurity Monitoring System")
        app.setApplicationVersion("2.0")
        app.setOrganizationName("CyberSec Solutions")
        
        # Set application style
        app.setStyle('Fusion')
        
        # Create splash screen
        splash_text = """
        <div style='text-align: center; color: white; font-family: Arial;'>
        <h2>Cybersecurity Monitoring System</h2>
        <p>Version 2.0</p>
        <p>Loading components...</p>
        </div>
        """
        
        # Create a simple splash screen
        splash = QSplashScreen()
        splash.setStyleSheet("""
            QSplashScreen {
                background-color: #1E1E2E;
                color: white;
                font-size: 14px;
            }
        """)
        splash.showMessage("Loading Cybersecurity Monitoring System...", Qt.AlignCenter, Qt.white)
        splash.show()
        
        # Process events to show splash screen
        app.processEvents()
        
        # Import main application
        try:
            from main import DashboardApp
            logger.info("Main application module imported successfully")
        except ImportError as e:
            logger.error(f"Failed to import main application: {e}")
            QMessageBox.critical(None, "Import Error", f"Failed to import main application:\n{e}")
            sys.exit(1)
        
        # Create main window
        try:
            window = DashboardApp()
            logger.info("Main window created successfully")
        except Exception as e:
            logger.error(f"Failed to create main window: {e}")
            logger.error(traceback.format_exc())
            QMessageBox.critical(None, "Initialization Error", f"Failed to initialize application:\n{e}")
            sys.exit(1)
        
        # Close splash screen and show main window
        splash.finish(window)
        window.show()
        
        logger.info("Application started successfully")
        
        # Start application event loop
        exit_code = app.exec_()
        
        logger.info(f"Application exited with code: {exit_code}")
        return exit_code
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        logger.error(traceback.format_exc())
        
        # Try to show error dialog if PyQt5 is available
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox
            if QApplication.instance():
                QMessageBox.critical(None, "Critical Error", f"An unexpected error occurred:\n{e}")
        except:
            pass
        
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
