#!/usr/bin/env python3
"""
Quick Start Script for Cybersecurity Monitoring System
This script provides a quick way to launch the application with minimal setup.

⚠️ EDUCATIONAL PURPOSE DISCLAIMER ⚠️
This application is designed for educational purposes only.
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def quick_start():
    """Quick start function with minimal checks"""
    print("🔒 Cybersecurity Monitoring System - Quick Start")
    print("=" * 50)
    
    # Basic dependency check
    try:
        import PyQt5
        print("✅ PyQt5 found")
    except ImportError:
        print("❌ PyQt5 not found. Please install: pip install PyQt5")
        return False
    
    try:
        import psutil
        print("✅ psutil found")
    except ImportError:
        print("❌ psutil not found. Please install: pip install psutil")
        return False
    
    # Create basic directories
    for directory in ['data', 'logs', 'reports']:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Created {directory}/ directory")
    
    print("\n🚀 Starting application...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from main import DashboardApp
        
        app = QApplication(sys.argv)
        app.setApplicationName("Cybersecurity Monitoring System")
        
        window = DashboardApp()
        window.show()
        
        print("✅ Application started successfully!")
        print("📊 Educational cybersecurity dashboard is now running")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        return False

if __name__ == "__main__":
    try:
        exit_code = quick_start()
        sys.exit(exit_code if exit_code else 0)
    except KeyboardInterrupt:
        print("\n👋 Application closed by user")
        sys.exit(0)
