import os
import json
import hashlib
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTableWidget, 
                            QTableWidgetItem, QHeaderView, QPushButton, QComboBox,
                            QTextEdit, QGroupBox, QFormLayout, QCheckBox, QSpinBox,
                            QTabWidget, QProgressBar, QMessageBox, QDialog)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QColor, QFont
from database import DatabaseManager
import psutil

class ThreatDetector(QThread):
    threat_detected = pyqtSignal(str, str, str, str)  # type, severity, message, details
    
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.monitoring = False
        self.detection_rules = self.load_detection_rules()
    
    def load_detection_rules(self):
        """Load threat detection rules"""
        return {
            'high_cpu_usage': {'threshold': 90, 'duration': 300},  # 90% for 5 minutes
            'high_memory_usage': {'threshold': 95, 'duration': 180},  # 95% for 3 minutes
            'suspicious_network_activity': {'connections_threshold': 100},
            'unusual_process_activity': {'new_processes_threshold': 50},
            'disk_space_critical': {'threshold': 95},
            'failed_login_attempts': {'threshold': 5, 'timeframe': 300}  # 5 attempts in 5 minutes
        }
    
    def run(self):
        """Main threat detection loop"""
        self.monitoring = True
        
        while self.monitoring:
            try:
                # Check system resource threats
                self.check_resource_threats()
                
                # Check network threats
                self.check_network_threats()
                
                # Check process threats
                self.check_process_threats()
                
                # Check disk space threats
                self.check_disk_threats()
                
                # Sleep for 30 seconds before next check
                self.msleep(30000)
                
            except Exception as e:
                print(f"Threat detection error: {e}")
                self.msleep(5000)
    
    def check_resource_threats(self):
        """Check for resource-based threats"""
        try:
            # CPU usage check
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > self.detection_rules['high_cpu_usage']['threshold']:
                self.threat_detected.emit(
                    "RESOURCE", "HIGH", 
                    f"High CPU usage detected: {cpu_percent:.1f}%",
                    f"CPU usage has exceeded {self.detection_rules['high_cpu_usage']['threshold']}% threshold"
                )
            
            # Memory usage check
            memory = psutil.virtual_memory()
            if memory.percent > self.detection_rules['high_memory_usage']['threshold']:
                self.threat_detected.emit(
                    "RESOURCE", "CRITICAL", 
                    f"Critical memory usage: {memory.percent:.1f}%",
                    f"Memory usage has exceeded {self.detection_rules['high_memory_usage']['threshold']}% threshold"
                )
        
        except Exception as e:
            print(f"Resource threat check error: {e}")
    
    def check_network_threats(self):
        """Check for network-based threats"""
        try:
            # Count active connections
            connections = psutil.net_connections(kind='inet')
            active_connections = len([c for c in connections if c.status == psutil.CONN_ESTABLISHED])
            
            if active_connections > self.detection_rules['suspicious_network_activity']['connections_threshold']:
                self.threat_detected.emit(
                    "NETWORK", "MEDIUM", 
                    f"Suspicious network activity: {active_connections} active connections",
                    f"Number of active connections exceeds normal threshold of {self.detection_rules['suspicious_network_activity']['connections_threshold']}"
                )
            
            # Check for suspicious connections (simplified)
            for conn in connections:
                if conn.raddr and conn.status == psutil.CONN_ESTABLISHED:
                    remote_ip = conn.raddr.ip
                    # Check against known malicious IPs (simplified check)
                    if self.is_suspicious_ip(remote_ip):
                        self.threat_detected.emit(
                            "NETWORK", "HIGH", 
                            f"Connection to suspicious IP: {remote_ip}",
                            f"Active connection detected to potentially malicious IP address"
                        )
        
        except Exception as e:
            print(f"Network threat check error: {e}")
    
    def check_process_threats(self):
        """Check for process-based threats"""
        try:
            # Get current processes
            current_processes = set()
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    current_processes.add(proc.info['name'])
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Check for suspicious process names (simplified)
            suspicious_names = ['keylogger', 'trojan', 'backdoor', 'malware', 'virus']
            for proc_name in current_processes:
                if any(sus in proc_name.lower() for sus in suspicious_names):
                    self.threat_detected.emit(
                        "PROCESS", "CRITICAL", 
                        f"Suspicious process detected: {proc_name}",
                        f"Process with suspicious name pattern detected"
                    )
        
        except Exception as e:
            print(f"Process threat check error: {e}")
    
    def check_disk_threats(self):
        """Check for disk space threats"""
        try:
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    percent_used = (usage.used / usage.total) * 100
                    
                    if percent_used > self.detection_rules['disk_space_critical']['threshold']:
                        self.threat_detected.emit(
                            "DISK", "HIGH", 
                            f"Critical disk space: {percent_used:.1f}% used on {partition.device}",
                            f"Disk usage on {partition.device} has exceeded critical threshold"
                        )
                except PermissionError:
                    continue
        
        except Exception as e:
            print(f"Disk threat check error: {e}")
    
    def is_suspicious_ip(self, ip):
        """Check if IP is suspicious (simplified implementation)"""
        # This is a simplified check - in real implementation, you would check against
        # threat intelligence feeds, blacklists, etc.
        suspicious_ranges = [
            '10.0.0.',  # Example suspicious range
            '192.168.100.',  # Example suspicious range
        ]
        return any(ip.startswith(range_prefix) for range_prefix in suspicious_ranges)
    
    def stop_monitoring(self):
        """Stop threat monitoring"""
        self.monitoring = False

class AlertDetailsDialog(QDialog):
    def __init__(self, alert_data, parent=None):
        super().__init__(parent)
        self.alert_data = alert_data
        self.setup_ui()
    
    def setup_ui(self):
        self.setWindowTitle("Alert Details")
        self.setModal(True)
        self.resize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # Alert information
        info_group = QGroupBox("Alert Information")
        info_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #444;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        info_layout = QFormLayout(info_group)
        
        # Add alert details
        timestamp_label = QLabel(self.alert_data[1])  # timestamp
        timestamp_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        info_layout.addRow("Timestamp:", timestamp_label)
        
        type_label = QLabel(self.alert_data[2])  # alert_type
        type_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        info_layout.addRow("Type:", type_label)
        
        severity_label = QLabel(self.alert_data[3])  # severity
        severity_color = {"LOW": "#00AA00", "MEDIUM": "#FFAA00", "HIGH": "#FF5555", "CRITICAL": "#AA0000"}
        severity_label.setStyleSheet(f"color: {severity_color.get(self.alert_data[3], '#FFFFFF')}; font-size: 14px; font-weight: bold;")
        info_layout.addRow("Severity:", severity_label)
        
        message_label = QLabel(self.alert_data[4])  # message
        message_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        message_label.setWordWrap(True)
        info_layout.addRow("Message:", message_label)
        
        layout.addWidget(info_group)
        
        # Details
        details_group = QGroupBox("Additional Details")
        details_group.setStyleSheet(info_group.styleSheet())
        details_layout = QVBoxLayout(details_group)
        
        details_text = QTextEdit()
        details_text.setStyleSheet("""
            QTextEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        details_text.setPlainText(self.alert_data[5] if len(self.alert_data) > 5 else "No additional details available")
        details_text.setReadOnly(True)
        details_layout.addWidget(details_text)
        
        layout.addWidget(details_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        resolve_btn = QPushButton("Mark as Resolved")
        resolve_btn.setStyleSheet("""
            QPushButton {
                background-color: #00AA00;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #008800;
            }
        """)
        resolve_btn.clicked.connect(self.resolve_alert)
        
        close_btn = QPushButton("Close")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #3D3D4F;
            }
        """)
        close_btn.clicked.connect(self.close)
        
        button_layout.addWidget(resolve_btn)
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)
    
    def resolve_alert(self):
        """Mark alert as resolved"""
        # This would update the database to mark the alert as resolved
        QMessageBox.information(self, "Success", "Alert marked as resolved")
        self.accept()

class SecurityAlertsWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = DatabaseManager()
        self.threat_detector = None
        self.setup_ui()
        self.start_threat_detection()
        self.load_alerts()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Title
        title_label = QLabel("Security Alerts & Threat Detection")
        title_label.setStyleSheet("color: #FFFFFF; font-size: 24px; font-weight: bold;")
        layout.addWidget(title_label)
        
        # Create tabs
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #1A1A28;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #1E1E2E;
                color: #AAAAAA;
                padding: 8px 16px;
                margin-right: 4px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: #1A1A28;
                color: #FFFFFF;
            }
            QTabBar::tab:hover:!selected {
                background-color: #252535;
            }
        """)
        
        # Alerts tab
        alerts_tab = self.create_alerts_tab()
        tabs.addTab(alerts_tab, "Active Alerts")
        
        # Detection rules tab
        rules_tab = self.create_rules_tab()
        tabs.addTab(rules_tab, "Detection Rules")
        
        # Threat intelligence tab
        intel_tab = self.create_intel_tab()
        tabs.addTab(intel_tab, "Threat Intelligence")
        
        layout.addWidget(tabs)
    
    def create_alerts_tab(self):
        """Create alerts management tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Alert controls
        controls_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("Refresh Alerts")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #8E79E8;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7A67C7;
            }
        """)
        refresh_btn.clicked.connect(self.load_alerts)
        
        clear_resolved_btn = QPushButton("Clear Resolved")
        clear_resolved_btn.setStyleSheet("""
            QPushButton {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #3D3D4F;
            }
        """)
        clear_resolved_btn.clicked.connect(self.clear_resolved_alerts)
        
        # Filter by severity
        controls_layout.addWidget(QLabel("Filter by Severity:"))
        self.severity_filter = QComboBox()
        self.severity_filter.addItems(["All", "LOW", "MEDIUM", "HIGH", "CRITICAL"])
        self.severity_filter.setStyleSheet("""
            QComboBox {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        self.severity_filter.currentTextChanged.connect(self.filter_alerts)
        
        controls_layout.addWidget(self.severity_filter)
        controls_layout.addStretch()
        controls_layout.addWidget(refresh_btn)
        controls_layout.addWidget(clear_resolved_btn)
        
        layout.addLayout(controls_layout)
        
        # Alerts table
        self.alerts_table = QTableWidget()
        self.alerts_table.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: none;
                gridline-color: #333344;
            }
            QHeaderView::section {
                background-color: #1A1A28;
                color: #AAAAAA;
                border: none;
                padding: 8px;
                font-weight: bold;
            }
            QTableWidget::item {
                border-bottom: 1px solid #333344;
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #2D2D3F;
            }
        """)
        
        self.alerts_table.setColumnCount(6)
        self.alerts_table.setHorizontalHeaderLabels([
            "Timestamp", "Type", "Severity", "Message", "Status", "Actions"
        ])
        
        # Set column widths
        header = self.alerts_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Timestamp
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Severity
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # Message
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Status
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Actions
        
        self.alerts_table.verticalHeader().setVisible(False)
        self.alerts_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.alerts_table.itemDoubleClicked.connect(self.show_alert_details)
        
        layout.addWidget(self.alerts_table)
        
        return tab
    
    def create_rules_tab(self):
        """Create detection rules configuration tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        rules_label = QLabel("Threat Detection Rules Configuration")
        rules_label.setStyleSheet("color: #FFFFFF; font-size: 18px; font-weight: bold;")
        layout.addWidget(rules_label)
        
        # Rules configuration (placeholder)
        rules_info = QTextEdit()
        rules_info.setStyleSheet("""
            QTextEdit {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 10px;
                font-size: 14px;
            }
        """)
        rules_info.setReadOnly(True)
        rules_info.setText("""Detection Rules Configuration:

• CPU Usage Threshold: 90% for 5 minutes
• Memory Usage Threshold: 95% for 3 minutes  
• Network Connections Threshold: 100 active connections
• Disk Space Critical: 95% usage
• Failed Login Attempts: 5 attempts in 5 minutes

Rules can be customized based on your security requirements and system specifications.""")
        layout.addWidget(rules_info)
        
        return tab
    
    def create_intel_tab(self):
        """Create threat intelligence tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        intel_label = QLabel("Threat Intelligence")
        intel_label.setStyleSheet("color: #FFFFFF; font-size: 18px; font-weight: bold;")
        layout.addWidget(intel_label)
        
        # Threat intelligence info (placeholder)
        intel_info = QTextEdit()
        intel_info.setStyleSheet("""
            QTextEdit {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 10px;
                font-size: 14px;
            }
        """)
        intel_info.setReadOnly(True)
        intel_info.setText("""Threat Intelligence Sources:

• Malicious IP addresses database
• Known malware signatures
• Suspicious process patterns
• Network traffic anomalies
• File integrity monitoring

Intelligence feeds are updated regularly to ensure protection against the latest threats.""")
        layout.addWidget(intel_info)
        
        return tab
    
    def start_threat_detection(self):
        """Start threat detection engine"""
        if not self.threat_detector:
            self.threat_detector = ThreatDetector(self.db)
            self.threat_detector.threat_detected.connect(self.handle_threat_detection)
            self.threat_detector.start()
    
    def handle_threat_detection(self, alert_type, severity, message, details):
        """Handle detected threats"""
        # Store alert in database
        self.db.insert_security_alert(alert_type, severity, message, details)
        
        # Refresh alerts table
        self.load_alerts()
        
        # Show notification (optional)
        # QMessageBox.warning(self, f"{severity} Alert", message)
    
    def load_alerts(self):
        """Load alerts from database"""
        alerts = self.db.get_security_alerts()
        self.alerts_table.setRowCount(len(alerts))
        
        for row, alert in enumerate(alerts):
            # alert = (id, timestamp, alert_type, severity, message, details, resolved)
            self.alerts_table.setItem(row, 0, QTableWidgetItem(alert[1][:19]))  # Timestamp
            self.alerts_table.setItem(row, 1, QTableWidgetItem(alert[2]))  # Type
            
            # Severity with color coding
            severity_item = QTableWidgetItem(alert[3])
            severity_colors = {"LOW": "#00AA00", "MEDIUM": "#FFAA00", "HIGH": "#FF5555", "CRITICAL": "#AA0000"}
            severity_item.setForeground(QColor(severity_colors.get(alert[3], "#FFFFFF")))
            self.alerts_table.setItem(row, 2, severity_item)
            
            self.alerts_table.setItem(row, 3, QTableWidgetItem(alert[4]))  # Message
            
            # Status
            status = "Resolved" if len(alert) > 6 and alert[6] else "Active"
            status_item = QTableWidgetItem(status)
            status_item.setForeground(QColor("#00AA00" if status == "Resolved" else "#FF5555"))
            self.alerts_table.setItem(row, 4, status_item)
            
            # Actions button (placeholder)
            self.alerts_table.setItem(row, 5, QTableWidgetItem("View Details"))
            
            # Store alert ID for reference
            self.alerts_table.item(row, 0).setData(Qt.UserRole, alert[0])
    
    def filter_alerts(self):
        """Filter alerts by severity"""
        filter_text = self.severity_filter.currentText()
        
        for row in range(self.alerts_table.rowCount()):
            if filter_text == "All":
                self.alerts_table.setRowHidden(row, False)
            else:
                severity_item = self.alerts_table.item(row, 2)
                if severity_item and severity_item.text() == filter_text:
                    self.alerts_table.setRowHidden(row, False)
                else:
                    self.alerts_table.setRowHidden(row, True)
    
    def show_alert_details(self, item):
        """Show detailed alert information"""
        row = item.row()
        alert_id = self.alerts_table.item(row, 0).data(Qt.UserRole)
        
        # Get full alert data
        alerts = self.db.get_security_alerts()
        alert_data = next((a for a in alerts if a[0] == alert_id), None)
        
        if alert_data:
            dialog = AlertDetailsDialog(alert_data, self)
            dialog.exec_()
            self.load_alerts()  # Refresh after potential resolution
    
    def clear_resolved_alerts(self):
        """Clear resolved alerts from display"""
        reply = QMessageBox.question(
            self, "Clear Resolved Alerts",
            "Are you sure you want to remove all resolved alerts from the database?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Delete resolved alerts from database
            query = "DELETE FROM security_alerts WHERE resolved = TRUE"
            self.db.execute_query(query)
            self.load_alerts()
            QMessageBox.information(self, "Success", "Resolved alerts cleared successfully!")
    
    def closeEvent(self, event):
        """Handle widget close event"""
        if self.threat_detector:
            self.threat_detector.stop_monitoring()
            self.threat_detector.wait()
        event.accept()
