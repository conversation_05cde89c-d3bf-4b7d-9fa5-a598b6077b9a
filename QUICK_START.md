# دليل البدء السريع - نظام مراقبة الأمان السيبراني

## التثبيت السريع

### 1. تحقق من متطلبات النظام
```bash
python --version  # يجب أن يكون 3.8 أو أحدث
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. اختبار سريع
```bash
python quick_test.py
```

### 4. تشغيل التطبيق
```bash
python run_app.py
```

## الوظائف الرئيسية

### 🖥️ مراقبة النظام
- **الوصول**: التبويب "System Monitor"
- **الوظائف**:
  - مراقبة استخدام المعالج والذاكرة
  - تتبع استخدام القرص الصلب
  - عرض العمليات النشطة
  - تنبيهات تلقائية عند تجاوز الحدود

### 🔐 إدارة كلمات المرور
- **الوصول**: التبويب "Password Manager"
- **الوظائف**:
  - تخزين آمن ومشفر لكلمات المرور
  - مولد كلمات مرور قوية
  - فحص قوة كلمات المرور
  - تصنيف وبحث في كلمات المرور

### 🌐 مراقبة الشبكة
- **الوصول**: التبويب "Network Monitor"
- **الوظائف**:
  - عرض الاتصالات النشطة
  - فحص الشبكة المحلية
  - فحص المنافذ المفتوحة
  - مراقبة حركة البيانات

### 🚨 التنبيهات الأمنية
- **الوصول**: التبويب "Security Alerts"
- **الوظائف**:
  - كشف التهديدات في الوقت الفعلي
  - تنبيهات الاستخدام المرتفع
  - مراقبة الأنشطة المشبوهة
  - إدارة وحل التنبيهات

### 📊 التقارير والتحليلات
- **الوصول**: التبويب "Reports"
- **الوظائف**:
  - إنشاء تقارير مفصلة
  - تصدير البيانات
  - تحليل الأداء التاريخي
  - رسوم بيانية تفاعلية

## نصائح الاستخدام

### للحصول على أفضل النتائج:
1. **تشغيل كمدير**: للوصول الكامل لمراقبة النظام
2. **ترك التطبيق يعمل**: للحصول على بيانات مستمرة
3. **مراجعة التنبيهات**: بانتظام للحفاظ على الأمان
4. **إنشاء تقارير**: دورية لتتبع الأداء

### إعدادات مهمة:
- **حدود التنبيهات**: يمكن تخصيصها في الإعدادات
- **فترات المراقبة**: قابلة للتعديل حسب الحاجة
- **تشفير البيانات**: مفعل تلقائياً لكلمات المرور

## استكشاف الأخطاء السريع

### مشكلة: التطبيق لا يبدأ
```bash
# تحقق من المتطلبات
python quick_test.py

# إعادة تثبيت المتطلبات
pip install --upgrade -r requirements.txt
```

### مشكلة: لا توجد بيانات مراقبة
- تأكد من تشغيل التطبيق كمدير
- انتظر بضع دقائق لجمع البيانات
- تحقق من التبويب "System Monitor"

### مشكلة: خطأ في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm -rf data/
python run_app.py
```

### مشكلة: التنبيهات لا تعمل
- تحقق من إعدادات الحدود
- تأكد من تشغيل مراقبة النظام
- راجع سجلات التطبيق في مجلد logs/

## الأمان والخصوصية

### ✅ آمن:
- جميع البيانات محلية
- كلمات المرور مشفرة
- لا يتصل بالإنترنت إلا عند الحاجة
- مفاتيح التشفير محمية

### ⚠️ تحذيرات:
- استخدم فقط للمراقبة المشروعة
- لا تشارك ملفات قاعدة البيانات
- احتفظ بنسخ احتياطية من البيانات المهمة

## الدعم

### للحصول على المساعدة:
1. راجع ملف README.md الكامل
2. تحقق من ملفات السجل في logs/
3. شغل الاختبارات: `python test_app.py`
4. أبلغ عن المشاكل في GitHub

### ملفات مهمة:
- `logs/`: سجلات التطبيق
- `data/`: قاعدة البيانات والبيانات المحفوظة
- `reports/`: التقارير المُنتجة
- `config.json`: إعدادات التطبيق (يُنشأ تلقائياً)

---

**نصيحة**: ابدأ بتشغيل `python quick_test.py` للتأكد من أن كل شيء يعمل بشكل صحيح قبل تشغيل التطبيق الكامل.
