"""
Configuration settings for Cybersecurity Monitoring System
"""

import os
import json
from datetime import timedelta

class Config:
    """Application configuration class"""
    
    # Application Information
    APP_NAME = "Cybersecurity Monitoring System"
    APP_VERSION = "2.0"
    APP_AUTHOR = "CyberSec Solutions"
    
    # Database Configuration
    DATABASE_PATH = "data/cybersecurity.db"
    ENCRYPTION_KEY_PATH = "data/encryption.key"
    
    # Monitoring Configuration
    SYSTEM_MONITOR_INTERVAL = 5  # seconds
    NETWORK_MONITOR_INTERVAL = 10  # seconds
    THREAT_DETECTION_INTERVAL = 30  # seconds
    
    # Alert Thresholds
    CPU_THRESHOLD = 80.0  # percentage
    MEMORY_THRESHOLD = 85.0  # percentage
    DISK_THRESHOLD = 90.0  # percentage
    NETWORK_CONNECTIONS_THRESHOLD = 100
    
    # Data Retention
    SYSTEM_DATA_RETENTION_DAYS = 30
    ALERT_DATA_RETENTION_DAYS = 90
    LOG_RETENTION_DAYS = 7
    
    # UI Configuration
    WINDOW_WIDTH = 1400
    WINDOW_HEIGHT = 900
    THEME = "dark"
    
    # Logging Configuration
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_FILE_BACKUP_COUNT = 5
    
    # Security Configuration
    PASSWORD_MIN_LENGTH = 8
    PASSWORD_REQUIRE_UPPERCASE = True
    PASSWORD_REQUIRE_LOWERCASE = True
    PASSWORD_REQUIRE_NUMBERS = True
    PASSWORD_REQUIRE_SYMBOLS = True
    
    # Network Scanner Configuration
    DEFAULT_NETWORK_RANGE = "192.168.1.0/24"
    PORT_SCAN_TIMEOUT = 0.5  # seconds
    NETWORK_SCAN_TIMEOUT = 1.0  # seconds
    
    # Report Configuration
    REPORT_OUTPUT_DIR = "reports"
    CHART_DPI = 300
    CHART_FORMAT = "png"
    
    @classmethod
    def load_from_file(cls, config_file="config.json"):
        """Load configuration from JSON file"""
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    config_data = json.load(f)
                
                # Update class attributes with loaded values
                for key, value in config_data.items():
                    if hasattr(cls, key):
                        setattr(cls, key, value)
                
                return True
            except Exception as e:
                print(f"Error loading config file: {e}")
                return False
        return False
    
    @classmethod
    def save_to_file(cls, config_file="config.json"):
        """Save current configuration to JSON file"""
        try:
            config_data = {}
            
            # Get all class attributes that are configuration values
            for attr_name in dir(cls):
                if not attr_name.startswith('_') and not callable(getattr(cls, attr_name)):
                    attr_value = getattr(cls, attr_name)
                    if isinstance(attr_value, (str, int, float, bool, list, dict)):
                        config_data[attr_name] = attr_value
            
            with open(config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            return True
        except Exception as e:
            print(f"Error saving config file: {e}")
            return False
    
    @classmethod
    def get_database_url(cls):
        """Get database connection URL"""
        return f"sqlite:///{cls.DATABASE_PATH}"
    
    @classmethod
    def get_log_file_path(cls):
        """Get log file path"""
        from datetime import datetime
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        log_filename = f"cybersecurity_{datetime.now().strftime('%Y%m%d')}.log"
        return os.path.join(log_dir, log_filename)
    
    @classmethod
    def ensure_directories(cls):
        """Ensure all required directories exist"""
        directories = [
            "data",
            "logs", 
            "reports",
            "backups",
            os.path.dirname(cls.DATABASE_PATH),
            os.path.dirname(cls.ENCRYPTION_KEY_PATH),
            cls.REPORT_OUTPUT_DIR
        ]
        
        for directory in directories:
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
    
    @classmethod
    def validate_config(cls):
        """Validate configuration values"""
        errors = []
        
        # Validate thresholds
        if not (0 <= cls.CPU_THRESHOLD <= 100):
            errors.append("CPU_THRESHOLD must be between 0 and 100")
        
        if not (0 <= cls.MEMORY_THRESHOLD <= 100):
            errors.append("MEMORY_THRESHOLD must be between 0 and 100")
        
        if not (0 <= cls.DISK_THRESHOLD <= 100):
            errors.append("DISK_THRESHOLD must be between 0 and 100")
        
        # Validate intervals
        if cls.SYSTEM_MONITOR_INTERVAL < 1:
            errors.append("SYSTEM_MONITOR_INTERVAL must be at least 1 second")
        
        if cls.NETWORK_MONITOR_INTERVAL < 1:
            errors.append("NETWORK_MONITOR_INTERVAL must be at least 1 second")
        
        # Validate retention periods
        if cls.SYSTEM_DATA_RETENTION_DAYS < 1:
            errors.append("SYSTEM_DATA_RETENTION_DAYS must be at least 1 day")
        
        # Validate window dimensions
        if cls.WINDOW_WIDTH < 800:
            errors.append("WINDOW_WIDTH must be at least 800 pixels")
        
        if cls.WINDOW_HEIGHT < 600:
            errors.append("WINDOW_HEIGHT must be at least 600 pixels")
        
        return errors
    
    @classmethod
    def reset_to_defaults(cls):
        """Reset configuration to default values"""
        # This would reset all values to their defaults
        # Implementation depends on how you want to handle defaults
        pass

# Load configuration on import
Config.load_from_file()
Config.ensure_directories()

# Validate configuration
config_errors = Config.validate_config()
if config_errors:
    print("Configuration validation errors:")
    for error in config_errors:
        print(f"  - {error}")
