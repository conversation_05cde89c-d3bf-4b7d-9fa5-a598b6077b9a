#!/usr/bin/env python3
"""
Cybersecurity Monitoring System - Headless Mode
Run the monitoring system without GUI for server environments
"""

import sys
import os
import time
import signal
import threading
from datetime import datetime

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

class HeadlessMonitor:
    def __init__(self):
        self.running = False
        self.db = None
        self.system_monitor = None
        self.threat_detector = None
        
    def setup(self):
        """Setup monitoring components"""
        try:
            from database import DatabaseManager
            from system_monitor import SystemMonitor
            from security_alerts import ThreatDetector
            
            print("Initializing Cybersecurity Monitoring System (Headless Mode)")
            print("=" * 60)
            
            # Initialize database
            self.db = DatabaseManager()
            print("✓ Database initialized")
            
            # Initialize system monitor
            self.system_monitor = SystemMonitor()
            self.system_monitor.system_data_updated.connect(self.on_system_data)
            self.system_monitor.alert_generated.connect(self.on_alert)
            print("✓ System monitor initialized")
            
            # Initialize threat detector
            self.threat_detector = ThreatDetector(self.db)
            self.threat_detector.threat_detected.connect(self.on_threat)
            print("✓ Threat detector initialized")
            
            return True
            
        except Exception as e:
            print(f"✗ Setup failed: {e}")
            return False
    
    def start(self):
        """Start monitoring"""
        if not self.setup():
            return False
        
        self.running = True
        
        # Start system monitoring
        self.system_monitor.start_monitoring(interval=10)
        print("✓ System monitoring started")
        
        # Start threat detection
        self.threat_detector.start()
        print("✓ Threat detection started")
        
        print("\n" + "=" * 60)
        print("Cybersecurity Monitoring System is now running...")
        print("Press Ctrl+C to stop")
        print("=" * 60)
        
        # Main monitoring loop
        try:
            while self.running:
                self.print_status()
                time.sleep(30)  # Update every 30 seconds
                
        except KeyboardInterrupt:
            print("\n\nShutdown requested...")
            self.stop()
        
        return True
    
    def stop(self):
        """Stop monitoring"""
        self.running = False
        
        if self.system_monitor:
            self.system_monitor.stop_monitoring()
            print("✓ System monitoring stopped")
        
        if self.threat_detector:
            self.threat_detector.stop_monitoring()
            self.threat_detector.wait()
            print("✓ Threat detection stopped")
        
        print("✓ Cybersecurity Monitoring System stopped")
    
    def on_system_data(self, data):
        """Handle system data updates"""
        # Store data in database (already handled by system_monitor)
        pass
    
    def on_alert(self, alert_type, severity, message):
        """Handle system alerts"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"\n🚨 ALERT [{timestamp}] {severity}: {message}")
    
    def on_threat(self, alert_type, severity, message, details):
        """Handle threat detection"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"\n⚠️  THREAT [{timestamp}] {severity}: {message}")
        if details:
            print(f"   Details: {details}")
    
    def print_status(self):
        """Print current system status"""
        try:
            if self.system_monitor:
                data = self.system_monitor.collect_system_data()
                if data:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    print(f"\n[{timestamp}] System Status:")
                    print(f"  CPU: {data['cpu_percent']:.1f}%")
                    print(f"  Memory: {data['memory_percent']:.1f}%")
                    print(f"  Disk: {data['disk_percent']:.1f}%")
                    print(f"  Active Processes: {data['active_processes']}")
                    
                    # Get recent alerts
                    alerts = self.db.get_security_alerts()
                    unresolved = [a for a in alerts if not (len(a) > 6 and a[6])]
                    if unresolved:
                        print(f"  Unresolved Alerts: {len(unresolved)}")
        except Exception as e:
            print(f"Error getting status: {e}")

def test_components():
    """Test individual components"""
    print("Testing components...")
    
    try:
        # Test database
        from database import DatabaseManager
        db = DatabaseManager("test_headless.db")
        db.insert_system_monitoring(50.0, 60.0, 70.0, 1000, 2000, 100)
        print("✓ Database test passed")
        
        # Clean up test database
        if os.path.exists("test_headless.db"):
            os.remove("test_headless.db")
        
        # Test system monitor
        from system_monitor import SystemMonitor
        monitor = SystemMonitor()
        data = monitor.collect_system_data()
        if data:
            print("✓ System monitor test passed")
        
        # Test password generator
        from password_manager import PasswordGenerator
        password = PasswordGenerator.generate_password(12)
        if len(password) == 12:
            print("✓ Password generator test passed")
        
        return True
        
    except Exception as e:
        print(f"✗ Component test failed: {e}")
        return False

def main():
    """Main function"""
    print("Cybersecurity Monitoring System - Headless Mode")
    print("=" * 60)
    
    # Check if GUI mode was requested
    if len(sys.argv) > 1 and sys.argv[1] == "--gui":
        print("Starting GUI mode...")
        try:
            from PyQt5.QtWidgets import QApplication
            app = QApplication(sys.argv)
            
            from main import DashboardApp
            window = DashboardApp()
            window.show()
            
            return app.exec_()
        except ImportError:
            print("PyQt5 not available. Running in headless mode...")
        except Exception as e:
            print(f"GUI mode failed: {e}. Running in headless mode...")
    
    # Test components first
    if not test_components():
        print("Component tests failed. Exiting...")
        return 1
    
    # Run headless monitoring
    monitor = HeadlessMonitor()
    
    # Setup signal handlers
    def signal_handler(signum, frame):
        print(f"\nReceived signal {signum}")
        monitor.stop()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start monitoring
    success = monitor.start()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
