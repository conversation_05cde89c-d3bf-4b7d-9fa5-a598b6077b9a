import psutil
import socket
import threading
import time
import requests
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTableWidget, 
                            QTableWidgetItem, QHeaderView, QPushButton, QComboBox,
                            QLineEdit, QGroupBox, QProgressBar, QTextEdit, QTabWidget)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QColor, QFont
from database import DatabaseManager

class NetworkScanner(QThread):
    scan_progress = pyqtSignal(int)
    device_found = pyqtSignal(str, str, str)  # IP, hostname, status
    scan_completed = pyqtSignal()
    
    def __init__(self, network_range="***********/24"):
        super().__init__()
        self.network_range = network_range
        self.scanning = False
    
    def run(self):
        """Scan network for active devices"""
        self.scanning = True
        import ipaddress
        
        try:
            network = ipaddress.IPv4Network(self.network_range, strict=False)
            total_hosts = network.num_addresses
            scanned = 0
            
            for ip in network.hosts():
                if not self.scanning:
                    break
                
                ip_str = str(ip)
                self.scan_host(ip_str)
                
                scanned += 1
                progress = int((scanned / total_hosts) * 100)
                self.scan_progress.emit(progress)
                
                # Small delay to prevent overwhelming the network
                time.sleep(0.1)
            
            self.scan_completed.emit()
        except Exception as e:
            print(f"Network scan error: {e}")
    
    def scan_host(self, ip):
        """Scan a single host"""
        try:
            # Try to ping the host (simplified check)
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((ip, 80))  # Try port 80
            sock.close()
            
            if result == 0:
                status = "Active"
            else:
                # Try another common port
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((ip, 22))  # Try SSH port
                sock.close()
                status = "Active" if result == 0 else "Unknown"
            
            # Try to get hostname
            try:
                hostname = socket.gethostbyaddr(ip)[0]
            except:
                hostname = "Unknown"
            
            if status == "Active":
                self.device_found.emit(ip, hostname, status)
                
        except Exception as e:
            pass
    
    def stop_scan(self):
        """Stop the network scan"""
        self.scanning = False

class PortScanner(QThread):
    port_found = pyqtSignal(int, str)  # port, service
    scan_progress = pyqtSignal(int)
    scan_completed = pyqtSignal()
    
    def __init__(self, target_ip, port_range=(1, 1024)):
        super().__init__()
        self.target_ip = target_ip
        self.start_port, self.end_port = port_range
        self.scanning = False
    
    def run(self):
        """Scan ports on target IP"""
        self.scanning = True
        total_ports = self.end_port - self.start_port + 1
        scanned = 0
        
        for port in range(self.start_port, self.end_port + 1):
            if not self.scanning:
                break
            
            if self.scan_port(port):
                service = self.get_service_name(port)
                self.port_found.emit(port, service)
            
            scanned += 1
            progress = int((scanned / total_ports) * 100)
            self.scan_progress.emit(progress)
            
            time.sleep(0.01)  # Small delay
        
        self.scan_completed.emit()
    
    def scan_port(self, port):
        """Scan a single port"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(0.5)
            result = sock.connect_ex((self.target_ip, port))
            sock.close()
            return result == 0
        except:
            return False
    
    def get_service_name(self, port):
        """Get service name for port"""
        common_ports = {
            21: "FTP", 22: "SSH", 23: "Telnet", 25: "SMTP", 53: "DNS",
            80: "HTTP", 110: "POP3", 143: "IMAP", 443: "HTTPS", 993: "IMAPS",
            995: "POP3S", 3389: "RDP", 5432: "PostgreSQL", 3306: "MySQL"
        }
        return common_ports.get(port, "Unknown")
    
    def stop_scan(self):
        """Stop the port scan"""
        self.scanning = False

class NetworkMonitorWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = DatabaseManager()
        self.setup_ui()
        self.start_monitoring()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Title
        title_label = QLabel("Network Monitor")
        title_label.setStyleSheet("color: #FFFFFF; font-size: 24px; font-weight: bold;")
        layout.addWidget(title_label)
        
        # Create tabs
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #1A1A28;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #1E1E2E;
                color: #AAAAAA;
                padding: 8px 16px;
                margin-right: 4px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: #1A1A28;
                color: #FFFFFF;
            }
            QTabBar::tab:hover:!selected {
                background-color: #252535;
            }
        """)
        
        # Connections tab
        connections_tab = self.create_connections_tab()
        tabs.addTab(connections_tab, "Active Connections")
        
        # Network scan tab
        scan_tab = self.create_scan_tab()
        tabs.addTab(scan_tab, "Network Scanner")
        
        # Port scan tab
        port_scan_tab = self.create_port_scan_tab()
        tabs.addTab(port_scan_tab, "Port Scanner")
        
        # Traffic monitor tab
        traffic_tab = self.create_traffic_tab()
        tabs.addTab(traffic_tab, "Traffic Monitor")
        
        layout.addWidget(tabs)
    
    def create_connections_tab(self):
        """Create active connections tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Refresh button
        refresh_btn = QPushButton("Refresh Connections")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #8E79E8;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7A67C7;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_connections)
        layout.addWidget(refresh_btn)
        
        # Connections table
        self.connections_table = QTableWidget()
        self.connections_table.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: none;
                gridline-color: #333344;
            }
            QHeaderView::section {
                background-color: #1A1A28;
                color: #AAAAAA;
                border: none;
                padding: 8px;
                font-weight: bold;
            }
            QTableWidget::item {
                border-bottom: 1px solid #333344;
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #2D2D3F;
            }
        """)
        
        self.connections_table.setColumnCount(7)
        self.connections_table.setHorizontalHeaderLabels([
            "Local IP", "Local Port", "Remote IP", "Remote Port", "Protocol", "Status", "Process"
        ])
        
        # Set column widths
        header = self.connections_table.horizontalHeader()
        for i in range(7):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)
        
        self.connections_table.verticalHeader().setVisible(False)
        self.connections_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.connections_table)
        
        return tab
    
    def create_scan_tab(self):
        """Create network scanner tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Scan controls
        controls_group = QGroupBox("Network Scan Settings")
        controls_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #444;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        controls_layout = QHBoxLayout(controls_group)
        
        # Network range input
        controls_layout.addWidget(QLabel("Network Range:"))
        self.network_range_input = QLineEdit("***********/24")
        self.network_range_input.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        controls_layout.addWidget(self.network_range_input)
        
        # Scan button
        self.scan_btn = QPushButton("Start Scan")
        self.scan_btn.setStyleSheet("""
            QPushButton {
                background-color: #8E79E8;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7A67C7;
            }
        """)
        self.scan_btn.clicked.connect(self.start_network_scan)
        controls_layout.addWidget(self.scan_btn)
        
        layout.addWidget(controls_group)
        
        # Progress bar
        self.scan_progress = QProgressBar()
        self.scan_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #444;
                border-radius: 5px;
                text-align: center;
                background-color: #2D2D3F;
                color: #FFFFFF;
            }
            QProgressBar::chunk {
                background-color: #8E79E8;
                border-radius: 5px;
            }
        """)
        self.scan_progress.setVisible(False)
        layout.addWidget(self.scan_progress)
        
        # Scan results table
        self.scan_results_table = QTableWidget()
        self.scan_results_table.setStyleSheet(self.connections_table.styleSheet())
        self.scan_results_table.setColumnCount(3)
        self.scan_results_table.setHorizontalHeaderLabels(["IP Address", "Hostname", "Status"])
        
        header = self.scan_results_table.horizontalHeader()
        for i in range(3):
            header.setSectionResizeMode(i, QHeaderView.Stretch)
        
        self.scan_results_table.verticalHeader().setVisible(False)
        layout.addWidget(self.scan_results_table)
        
        return tab
    
    def create_port_scan_tab(self):
        """Create port scanner tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Port scan controls
        controls_group = QGroupBox("Port Scan Settings")
        controls_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #444;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        controls_layout = QHBoxLayout(controls_group)
        
        # Target IP
        controls_layout.addWidget(QLabel("Target IP:"))
        self.target_ip_input = QLineEdit("127.0.0.1")
        self.target_ip_input.setStyleSheet(self.network_range_input.styleSheet())
        controls_layout.addWidget(self.target_ip_input)
        
        # Port range
        controls_layout.addWidget(QLabel("Port Range:"))
        self.port_range_combo = QComboBox()
        self.port_range_combo.addItems([
            "Common Ports (1-1024)",
            "All Ports (1-65535)",
            "Web Ports (80,443,8080,8443)",
            "Custom Range"
        ])
        self.port_range_combo.setStyleSheet("""
            QComboBox {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        controls_layout.addWidget(self.port_range_combo)
        
        # Port scan button
        self.port_scan_btn = QPushButton("Start Port Scan")
        self.port_scan_btn.setStyleSheet(self.scan_btn.styleSheet())
        self.port_scan_btn.clicked.connect(self.start_port_scan)
        controls_layout.addWidget(self.port_scan_btn)
        
        layout.addWidget(controls_group)
        
        # Port scan progress
        self.port_scan_progress = QProgressBar()
        self.port_scan_progress.setStyleSheet(self.scan_progress.styleSheet())
        self.port_scan_progress.setVisible(False)
        layout.addWidget(self.port_scan_progress)
        
        # Port scan results
        self.port_results_table = QTableWidget()
        self.port_results_table.setStyleSheet(self.connections_table.styleSheet())
        self.port_results_table.setColumnCount(2)
        self.port_results_table.setHorizontalHeaderLabels(["Port", "Service"])
        
        header = self.port_results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        self.port_results_table.verticalHeader().setVisible(False)
        layout.addWidget(self.port_results_table)
        
        return tab
    
    def create_traffic_tab(self):
        """Create traffic monitor tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Traffic statistics
        stats_label = QLabel("Network Traffic Statistics")
        stats_label.setStyleSheet("color: #FFFFFF; font-size: 18px; font-weight: bold;")
        layout.addWidget(stats_label)
        
        # Placeholder for traffic charts
        traffic_info = QTextEdit()
        traffic_info.setStyleSheet("""
            QTextEdit {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 10px;
                font-size: 14px;
            }
        """)
        traffic_info.setReadOnly(True)
        traffic_info.setText("Real-time network traffic monitoring will be displayed here.\n\nFeatures:\n• Bandwidth usage monitoring\n• Protocol analysis\n• Traffic flow visualization\n• Historical data tracking")
        layout.addWidget(traffic_info)
        
        return tab
    
    def start_monitoring(self):
        """Start network monitoring"""
        # Set up timer for periodic updates
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.refresh_connections)
        self.monitor_timer.start(10000)  # Update every 10 seconds
        
        # Initial load
        self.refresh_connections()
    
    def refresh_connections(self):
        """Refresh active connections"""
        try:
            connections = []
            for conn in psutil.net_connections(kind='inet'):
                if conn.status == psutil.CONN_ESTABLISHED:
                    try:
                        process = psutil.Process(conn.pid) if conn.pid else None
                        process_name = process.name() if process else "Unknown"
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        process_name = "Unknown"
                    
                    connections.append({
                        'local_ip': conn.laddr.ip if conn.laddr else "",
                        'local_port': conn.laddr.port if conn.laddr else 0,
                        'remote_ip': conn.raddr.ip if conn.raddr else "",
                        'remote_port': conn.raddr.port if conn.raddr else 0,
                        'protocol': 'TCP' if conn.type == socket.SOCK_STREAM else 'UDP',
                        'status': conn.status,
                        'process': process_name
                    })
            
            # Update table
            self.connections_table.setRowCount(len(connections))
            for row, conn in enumerate(connections):
                self.connections_table.setItem(row, 0, QTableWidgetItem(conn['local_ip']))
                self.connections_table.setItem(row, 1, QTableWidgetItem(str(conn['local_port'])))
                self.connections_table.setItem(row, 2, QTableWidgetItem(conn['remote_ip']))
                self.connections_table.setItem(row, 3, QTableWidgetItem(str(conn['remote_port'])))
                self.connections_table.setItem(row, 4, QTableWidgetItem(conn['protocol']))
                self.connections_table.setItem(row, 5, QTableWidgetItem(conn['status']))
                self.connections_table.setItem(row, 6, QTableWidgetItem(conn['process']))
        
        except Exception as e:
            print(f"Error refreshing connections: {e}")
    
    def start_network_scan(self):
        """Start network scan"""
        if hasattr(self, 'network_scanner') and self.network_scanner.isRunning():
            self.network_scanner.stop_scan()
            self.scan_btn.setText("Start Scan")
            return
        
        network_range = self.network_range_input.text()
        self.scan_results_table.setRowCount(0)
        
        self.network_scanner = NetworkScanner(network_range)
        self.network_scanner.scan_progress.connect(self.scan_progress.setValue)
        self.network_scanner.device_found.connect(self.add_scan_result)
        self.network_scanner.scan_completed.connect(self.on_scan_completed)
        
        self.scan_progress.setVisible(True)
        self.scan_progress.setValue(0)
        self.scan_btn.setText("Stop Scan")
        
        self.network_scanner.start()
    
    def add_scan_result(self, ip, hostname, status):
        """Add scan result to table"""
        row = self.scan_results_table.rowCount()
        self.scan_results_table.insertRow(row)
        self.scan_results_table.setItem(row, 0, QTableWidgetItem(ip))
        self.scan_results_table.setItem(row, 1, QTableWidgetItem(hostname))
        self.scan_results_table.setItem(row, 2, QTableWidgetItem(status))
    
    def on_scan_completed(self):
        """Handle scan completion"""
        self.scan_progress.setVisible(False)
        self.scan_btn.setText("Start Scan")
    
    def start_port_scan(self):
        """Start port scan"""
        if hasattr(self, 'port_scanner') and self.port_scanner.isRunning():
            self.port_scanner.stop_scan()
            self.port_scan_btn.setText("Start Port Scan")
            return
        
        target_ip = self.target_ip_input.text()
        port_range_text = self.port_range_combo.currentText()
        
        # Determine port range
        if "Common Ports" in port_range_text:
            port_range = (1, 1024)
        elif "All Ports" in port_range_text:
            port_range = (1, 65535)
        elif "Web Ports" in port_range_text:
            port_range = (80, 443)  # Simplified for demo
        else:
            port_range = (1, 1024)  # Default
        
        self.port_results_table.setRowCount(0)
        
        self.port_scanner = PortScanner(target_ip, port_range)
        self.port_scanner.scan_progress.connect(self.port_scan_progress.setValue)
        self.port_scanner.port_found.connect(self.add_port_result)
        self.port_scanner.scan_completed.connect(self.on_port_scan_completed)
        
        self.port_scan_progress.setVisible(True)
        self.port_scan_progress.setValue(0)
        self.port_scan_btn.setText("Stop Port Scan")
        
        self.port_scanner.start()
    
    def add_port_result(self, port, service):
        """Add port scan result to table"""
        row = self.port_results_table.rowCount()
        self.port_results_table.insertRow(row)
        self.port_results_table.setItem(row, 0, QTableWidgetItem(str(port)))
        self.port_results_table.setItem(row, 1, QTableWidgetItem(service))
    
    def on_port_scan_completed(self):
        """Handle port scan completion"""
        self.port_scan_progress.setVisible(False)
        self.port_scan_btn.setText("Start Port Scan")
