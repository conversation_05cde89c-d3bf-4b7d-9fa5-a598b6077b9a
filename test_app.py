#!/usr/bin/env python3
"""
Test script for Cybersecurity Monitoring System
This script tests various components of the application
"""

import sys
import os
import unittest
import tempfile
import shutil
from datetime import datetime

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

class TestDatabaseManager(unittest.TestCase):
    """Test database functionality"""
    
    def setUp(self):
        """Set up test database"""
        self.test_db_path = tempfile.mktemp(suffix='.db')
        
    def tearDown(self):
        """Clean up test database"""
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
    
    def test_database_creation(self):
        """Test database creation and initialization"""
        try:
            from database import DatabaseManager
            db = DatabaseManager(self.test_db_path)
            self.assertTrue(os.path.exists(self.test_db_path))
            print("✓ Database creation test passed")
        except Exception as e:
            self.fail(f"Database creation failed: {e}")
    
    def test_data_insertion(self):
        """Test data insertion"""
        try:
            from database import DatabaseManager
            db = DatabaseManager(self.test_db_path)
            
            # Test system monitoring data insertion
            result = db.insert_system_monitoring(50.0, 60.0, 70.0, 1000, 2000, 100)
            self.assertIsNotNone(result)
            
            # Test password insertion
            result = db.insert_password("Test Site", "testuser", "testpass123", "https://test.com")
            self.assertIsNotNone(result)
            
            print("✓ Data insertion test passed")
        except Exception as e:
            self.fail(f"Data insertion failed: {e}")
    
    def test_data_retrieval(self):
        """Test data retrieval"""
        try:
            from database import DatabaseManager
            db = DatabaseManager(self.test_db_path)
            
            # Insert test data
            db.insert_system_monitoring(50.0, 60.0, 70.0, 1000, 2000, 100)
            db.insert_password("Test Site", "testuser", "testpass123", "https://test.com")
            
            # Retrieve data
            system_data = db.get_recent_system_data(24)
            passwords = db.get_passwords()
            
            self.assertGreater(len(system_data), 0)
            self.assertGreater(len(passwords), 0)
            
            print("✓ Data retrieval test passed")
        except Exception as e:
            self.fail(f"Data retrieval failed: {e}")

class TestPasswordManager(unittest.TestCase):
    """Test password manager functionality"""
    
    def test_password_generation(self):
        """Test password generation"""
        try:
            from password_manager import PasswordGenerator
            
            # Test basic password generation
            password = PasswordGenerator.generate_password(12)
            self.assertEqual(len(password), 12)
            
            # Test password with specific requirements
            password = PasswordGenerator.generate_password(
                16, use_uppercase=True, use_lowercase=True, 
                use_numbers=True, use_symbols=True
            )
            self.assertEqual(len(password), 16)
            
            print("✓ Password generation test passed")
        except Exception as e:
            self.fail(f"Password generation failed: {e}")
    
    def test_password_strength_check(self):
        """Test password strength checking"""
        try:
            from password_manager import PasswordGenerator
            
            # Test weak password
            weak_result = PasswordGenerator.check_password_strength("123")
            self.assertLess(weak_result['score'], 50)
            
            # Test strong password
            strong_result = PasswordGenerator.check_password_strength("MyStr0ng!P@ssw0rd")
            self.assertGreater(strong_result['score'], 70)
            
            print("✓ Password strength check test passed")
        except Exception as e:
            self.fail(f"Password strength check failed: {e}")

class TestSystemMonitor(unittest.TestCase):
    """Test system monitoring functionality"""
    
    def test_system_data_collection(self):
        """Test system data collection"""
        try:
            from system_monitor import SystemMonitor
            
            monitor = SystemMonitor()
            data = monitor.collect_system_data()
            
            self.assertIsNotNone(data)
            self.assertIn('cpu_percent', data)
            self.assertIn('memory_percent', data)
            self.assertIn('disk_percent', data)
            
            print("✓ System data collection test passed")
        except Exception as e:
            self.fail(f"System data collection failed: {e}")
    
    def test_network_connections(self):
        """Test network connections retrieval"""
        try:
            from system_monitor import SystemMonitor
            
            monitor = SystemMonitor()
            connections = monitor.get_network_connections()
            
            self.assertIsInstance(connections, list)
            
            print("✓ Network connections test passed")
        except Exception as e:
            self.fail(f"Network connections test failed: {e}")
    
    def test_running_processes(self):
        """Test running processes retrieval"""
        try:
            from system_monitor import SystemMonitor
            
            monitor = SystemMonitor()
            processes = monitor.get_running_processes()
            
            self.assertIsInstance(processes, list)
            self.assertGreater(len(processes), 0)
            
            print("✓ Running processes test passed")
        except Exception as e:
            self.fail(f"Running processes test failed: {e}")

class TestConfiguration(unittest.TestCase):
    """Test configuration management"""
    
    def test_config_loading(self):
        """Test configuration loading"""
        try:
            from config import Config
            
            # Test default values
            self.assertIsNotNone(Config.APP_NAME)
            self.assertIsNotNone(Config.APP_VERSION)
            self.assertGreater(Config.SYSTEM_MONITOR_INTERVAL, 0)
            
            print("✓ Configuration loading test passed")
        except Exception as e:
            self.fail(f"Configuration loading failed: {e}")
    
    def test_config_validation(self):
        """Test configuration validation"""
        try:
            from config import Config
            
            errors = Config.validate_config()
            # Should have no errors with default config
            self.assertEqual(len(errors), 0)
            
            print("✓ Configuration validation test passed")
        except Exception as e:
            self.fail(f"Configuration validation failed: {e}")

def test_dependencies():
    """Test if all required dependencies are available"""
    required_modules = [
        'PyQt5',
        'psutil', 
        'cryptography',
        'matplotlib',
        'pandas',
        'requests',
        'sqlite3'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module} is available")
        except ImportError:
            missing_modules.append(module)
            print(f"✗ {module} is missing")
    
    if missing_modules:
        print(f"\nMissing modules: {', '.join(missing_modules)}")
        print("Please install missing modules using:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    print("\n✓ All dependencies are available")
    return True

def test_file_structure():
    """Test if all required files are present"""
    required_files = [
        'src/main.py',
        'src/database.py',
        'src/system_monitor.py',
        'src/password_manager.py',
        'src/network_monitor.py',
        'src/security_alerts.py',
        'src/reports_generator.py',
        'src/config.py',
        'requirements.txt',
        'README.md'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} exists")
        else:
            missing_files.append(file_path)
            print(f"✗ {file_path} is missing")
    
    if missing_files:
        print(f"\nMissing files: {', '.join(missing_files)}")
        return False
    
    print("\n✓ All required files are present")
    return True

def run_basic_tests():
    """Run basic functionality tests"""
    print("Running basic functionality tests...\n")
    
    # Test dependencies
    if not test_dependencies():
        return False
    
    # Test file structure
    if not test_file_structure():
        return False
    
    # Run unit tests
    print("\nRunning unit tests...\n")
    
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestDatabaseManager))
    test_suite.addTest(unittest.makeSuite(TestPasswordManager))
    test_suite.addTest(unittest.makeSuite(TestSystemMonitor))
    test_suite.addTest(unittest.makeSuite(TestConfiguration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=0)
    result = runner.run(test_suite)
    
    if result.wasSuccessful():
        print("\n✓ All unit tests passed!")
        return True
    else:
        print(f"\n✗ {len(result.failures)} test(s) failed")
        print(f"✗ {len(result.errors)} error(s) occurred")
        return False

def main():
    """Main test function"""
    print("Cybersecurity Monitoring System - Test Suite")
    print("=" * 50)
    
    try:
        success = run_basic_tests()
        
        if success:
            print("\n" + "=" * 50)
            print("✓ All tests passed! The application should work correctly.")
            print("You can now run the application using: python run_app.py")
        else:
            print("\n" + "=" * 50)
            print("✗ Some tests failed. Please fix the issues before running the application.")
            return 1
        
    except Exception as e:
        print(f"\n✗ Test suite failed with error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
