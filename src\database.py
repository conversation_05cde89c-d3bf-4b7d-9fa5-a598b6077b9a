import sqlite3
import os
import hashlib
import json
from datetime import datetime
from cryptography.fernet import Fernet
import base64

class DatabaseManager:
    def __init__(self, db_path="data/cybersecurity.db"):
        self.db_path = db_path
        self.ensure_data_directory()
        self.init_database()
        self.encryption_key = self.get_or_create_encryption_key()
        self.cipher = Fernet(self.encryption_key)

    def ensure_data_directory(self):
        """Ensure the data directory exists"""
        data_dir = os.path.dirname(self.db_path)
        if data_dir and not os.path.exists(data_dir):
            os.makedirs(data_dir)

    def get_or_create_encryption_key(self):
        """Get or create encryption key for sensitive data"""
        key_file = "data/encryption.key"
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key

    def encrypt_data(self, data):
        """Encrypt sensitive data"""
        if isinstance(data, str):
            data = data.encode()
        return self.cipher.encrypt(data).decode()

    def decrypt_data(self, encrypted_data):
        """Decrypt sensitive data"""
        if isinstance(encrypted_data, str):
            encrypted_data = encrypted_data.encode()
        return self.cipher.decrypt(encrypted_data).decode()

    def init_database(self):
        """Initialize database with all required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # System monitoring table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_monitoring (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                cpu_percent REAL,
                memory_percent REAL,
                disk_percent REAL,
                network_sent INTEGER,
                network_received INTEGER,
                active_processes INTEGER
            )
        ''')

        # Network monitoring table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS network_monitoring (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                local_ip TEXT,
                remote_ip TEXT,
                local_port INTEGER,
                remote_port INTEGER,
                protocol TEXT,
                status TEXT,
                process_name TEXT
            )
        ''')

        # Password manager table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS passwords (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                username TEXT,
                password TEXT,
                url TEXT,
                notes TEXT,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                modified_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                category TEXT DEFAULT 'General'
            )
        ''')

        # Security alerts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                alert_type TEXT,
                severity TEXT,
                message TEXT,
                details TEXT,
                resolved BOOLEAN DEFAULT FALSE
            )
        ''')

        # System logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                log_level TEXT,
                source TEXT,
                message TEXT,
                details TEXT
            )
        ''')

        # User sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE,
                username TEXT,
                login_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT,
                user_agent TEXT,
                active BOOLEAN DEFAULT TRUE
            )
        ''')

        conn.commit()
        conn.close()

    def execute_query(self, query, params=None):
        """Execute a query and return results"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            if query.strip().upper().startswith('SELECT'):
                results = cursor.fetchall()
                conn.close()
                return results
            else:
                conn.commit()
                conn.close()
                return cursor.rowcount
        except Exception as e:
            conn.close()
            raise e

    def insert_system_monitoring(self, cpu_percent, memory_percent, disk_percent,
                                network_sent, network_received, active_processes):
        """Insert system monitoring data"""
        query = '''
            INSERT INTO system_monitoring
            (cpu_percent, memory_percent, disk_percent, network_sent, network_received, active_processes)
            VALUES (?, ?, ?, ?, ?, ?)
        '''
        return self.execute_query(query, (cpu_percent, memory_percent, disk_percent,
                                        network_sent, network_received, active_processes))

    def insert_network_monitoring(self, local_ip, remote_ip, local_port, remote_port,
                                protocol, status, process_name):
        """Insert network monitoring data"""
        query = '''
            INSERT INTO network_monitoring
            (local_ip, remote_ip, local_port, remote_port, protocol, status, process_name)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        '''
        return self.execute_query(query, (local_ip, remote_ip, local_port, remote_port,
                                        protocol, status, process_name))

    def insert_password(self, title, username, password, url="", notes="", category="General"):
        """Insert encrypted password"""
        encrypted_password = self.encrypt_data(password)
        query = '''
            INSERT INTO passwords (title, username, password, url, notes, category)
            VALUES (?, ?, ?, ?, ?, ?)
        '''
        return self.execute_query(query, (title, username, encrypted_password, url, notes, category))

    def get_passwords(self):
        """Get all passwords (decrypted)"""
        query = "SELECT id, title, username, password, url, notes, created_date, category FROM passwords"
        results = self.execute_query(query)

        decrypted_results = []
        for row in results:
            decrypted_row = list(row)
            decrypted_row[3] = self.decrypt_data(row[3])  # Decrypt password
            decrypted_results.append(tuple(decrypted_row))

        return decrypted_results

    def insert_security_alert(self, alert_type, severity, message, details=""):
        """Insert security alert"""
        query = '''
            INSERT INTO security_alerts (alert_type, severity, message, details)
            VALUES (?, ?, ?, ?)
        '''
        return self.execute_query(query, (alert_type, severity, message, details))

    def get_recent_system_data(self, hours=24):
        """Get recent system monitoring data"""
        query = '''
            SELECT * FROM system_monitoring
            WHERE timestamp >= datetime('now', '-{} hours')
            ORDER BY timestamp DESC
        '''.format(hours)
        return self.execute_query(query)

    def get_security_alerts(self, unresolved_only=False):
        """Get security alerts"""
        if unresolved_only:
            query = "SELECT * FROM security_alerts WHERE resolved = FALSE ORDER BY timestamp DESC"
        else:
            query = "SELECT * FROM security_alerts ORDER BY timestamp DESC"
        return self.execute_query(query)

    def cleanup_old_data(self, days=30):
        """Clean up old monitoring data"""
        queries = [
            "DELETE FROM system_monitoring WHERE timestamp < datetime('now', '-{} days')".format(days),
            "DELETE FROM network_monitoring WHERE timestamp < datetime('now', '-{} days')".format(days),
            "DELETE FROM system_logs WHERE timestamp < datetime('now', '-{} days')".format(days)
        ]

        for query in queries:
            self.execute_query(query)
