import os
import json
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QTableWidget, QTableWidgetItem, QHeaderView
from PyQt5.QtGui import QColor, QPixmap
from PyQt5.QtCore import Qt

class CountryLogsWidget(QWidget):
    def __init__(self, dashboard_components, parent=None):
        super().__init__(parent)
        self.dashboard = dashboard_components
        self.setup_ui()
    
    def setup_ui(self):
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(10)
        
        # Create country frame
        country_frame = QFrame()
        country_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        country_layout = QVBoxLayout(country_frame)
        country_layout.setContentsMargins(15, 15, 15, 15)
        
        # Add title
        title_label = QLabel("Country Logs")
        title_label.setStyleSheet("color: #FFFFFF; font-size: 16px; font-weight: bold;")
        country_layout.addWidget(title_label)
        
        # Add country table
        self.country_table = CountryTableEnhanced()
        country_layout.addWidget(self.country_table)
        
        # Add country statistics section
        stats_frame = QFrame()
        stats_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px; margin-top: 10px;")
        stats_layout = QVBoxLayout(stats_frame)
        
        stats_title = QLabel("Country Statistics")
        stats_title.setStyleSheet("color: #FFFFFF; font-size: 16px; font-weight: bold;")
        stats_layout.addWidget(stats_title)
        
        # Add country stats in horizontal layout
        stats_row = QHBoxLayout()
        
        # Create stat boxes for top countries
        self.country_stats = {}
        for i in range(4):
            stat_box = self.create_country_stat_box("--", "0")
            self.country_stats[i] = stat_box
            stats_row.addWidget(stat_box)
        
        stats_layout.addLayout(stats_row)
        
        # Add to main layout
        main_layout.addWidget(country_frame)
        main_layout.addWidget(stats_frame)
        
        # Initial update
        self.update_data()
    
    def create_country_stat_box(self, country, count):
        box = QFrame()
        box.setStyleSheet("background-color: #2D2D3F; border-radius: 5px;")
        box.setMinimumHeight(80)
        
        layout = QVBoxLayout(box)
        layout.setContentsMargins(10, 10, 10, 10)
        
        country_label = QLabel(country)
        country_label.setStyleSheet("color: #FFFFFF; font-size: 14px; font-weight: bold;")
        box.country_label = country_label
        
        count_label = QLabel(count)
        count_label.setStyleSheet("color: #8E79E8; font-size: 18px; font-weight: bold;")
        box.count_label = count_label
        
        layout.addWidget(country_label)
        layout.addWidget(count_label)
        
        return box
    
    def update_data(self):
        """Update all country data in the UI"""
        # Update country table
        self.country_table.update_data(self.dashboard.country_data)
        
        # Update country statistics
        # Sort countries by log count
        sorted_countries = sorted(
            self.dashboard.country_data.items(), 
            key=lambda x: x[1]["logs"], 
            reverse=True
        )
        
        # Update top 4 country stat boxes
        for i in range(min(4, len(sorted_countries))):
            country, data = sorted_countries[i]
            self.country_stats[i].country_label.setText(country)
            self.country_stats[i].count_label.setText(str(data["logs"]))

class CountryTableEnhanced(QTableWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: none;
                gridline-color: #333344;
            }
            QHeaderView::section {
                background-color: #1A1A28;
                color: #AAAAAA;
                border: none;
                padding: 5px;
            }
            QTableWidget::item {
                border-bottom: 1px solid #333344;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #2D2D3F;
            }
        """)
        
        # Set up headers
        self.setColumnCount(4)
        self.setHorizontalHeaderLabels(["Country", "Flags", "Code", "Logs"])
        
        # Set column widths
        header = self.horizontalHeader()
        for i in range(4):
            header.setSectionResizeMode(i, QHeaderView.Stretch)
        
        # No vertical header
        self.verticalHeader().setVisible(False)
        
        # Selection settings
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        
        # No editing
        self.setEditTriggers(QTableWidget.NoEditTriggers)
        
        # Set row height
        self.verticalHeader().setDefaultSectionSize(40)
    
    def update_data(self, country_data):
        """Update table with country data"""
        self.setRowCount(0)  # Clear existing rows
        
        # Sort countries by log count
        sorted_countries = sorted(
            country_data.items(), 
            key=lambda x: x[1]["logs"], 
            reverse=True
        )
        
        for row, (country, data) in enumerate(sorted_countries):
            self.insertRow(row)
            
            # Add data to cells
            country_item = QTableWidgetItem(country)
            country_item.setForeground(QColor("#FFFFFF"))
            self.setItem(row, 0, country_item)
            
            flag_item = QTableWidgetItem(data["flag"])
            flag_item.setForeground(QColor("#FFFFFF"))
            self.setItem(row, 1, flag_item)
            
            code_item = QTableWidgetItem(data["code"])
            code_item.setForeground(QColor("#AAAAAA"))
            self.setItem(row, 2, code_item)
            
            logs_item = QTableWidgetItem(str(data["logs"]))
            logs_item.setForeground(QColor("#8E79E8"))
            self.setItem(row, 3, logs_item)
