import os
import json
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QFrame, 
                            QCheckBox, QLineEdit, QPushButton, QComboBox, QTabWidget,
                            QScrollArea, QGroupBox, QFormLayout, QSpinBox, QFileDialog)
from PyQt5.QtGui import QColor, QPixmap, QIcon, QFont
from PyQt5.QtCore import Qt

class StealerSettingsWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Add title with educational disclaimer
        title_layout = QHBoxLayout()
        
        title_label = QLabel("Stealer Settings")
        title_label.setStyleSheet("color: #FFFFFF; font-size: 24px; font-weight: bold;")
        
        disclaimer_label = QLabel("(Educational Simulation)")
        disclaimer_label.setStyleSheet("color: #FF5555; font-size: 16px; font-style: italic;")
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(disclaimer_label)
        title_layout.addStretch()
        
        main_layout.addLayout(title_layout)
        
        # Add educational notice
        notice_frame = QFrame()
        notice_frame.setStyleSheet("background-color: #2D2D3F; border-radius: 5px;")
        notice_layout = QVBoxLayout(notice_frame)
        
        notice_title = QLabel("⚠️ Educational Notice")
        notice_title.setStyleSheet("color: #FFAA00; font-size: 16px; font-weight: bold;")
        
        notice_text = QLabel(
            "This is a simulated interface for educational purposes only. "
            "It demonstrates how malware settings might look, to help understand "
            "and protect against such threats. No actual harmful functionality is implemented."
        )
        notice_text.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        notice_text.setWordWrap(True)
        
        notice_layout.addWidget(notice_title)
        notice_layout.addWidget(notice_text)
        
        main_layout.addWidget(notice_frame)
        
        # Create tabs for different settings categories
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #1A1A28;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #1E1E2E;
                color: #AAAAAA;
                padding: 8px 16px;
                margin-right: 4px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: #1A1A28;
                color: #FFFFFF;
            }
            QTabBar::tab:hover:!selected {
                background-color: #252535;
            }
        """)
        
        # General Settings Tab
        general_tab = self.create_general_settings_tab()
        tabs.addTab(general_tab, "General")
        
        # Targets Tab
        targets_tab = self.create_targets_tab()
        tabs.addTab(targets_tab, "Targets")
        
        # Advanced Tab
        advanced_tab = self.create_advanced_tab()
        tabs.addTab(advanced_tab, "Advanced")
        
        # Anti-Detection Tab
        anti_detection_tab = self.create_anti_detection_tab()
        tabs.addTab(anti_detection_tab, "Anti-Detection")
        
        main_layout.addWidget(tabs)
        
        # Add action buttons
        buttons_layout = QHBoxLayout()
        
        save_button = QPushButton("Save Settings")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #8E79E8;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7A67C7;
            }
        """)
        save_button.clicked.connect(self.save_settings)
        
        reset_button = QPushButton("Reset to Defaults")
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #3D3D4F;
            }
        """)
        reset_button.clicked.connect(self.reset_settings)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(reset_button)
        buttons_layout.addStretch()
        
        main_layout.addLayout(buttons_layout)
        
        # Add educational information at the bottom
        info_frame = QFrame()
        info_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        info_layout = QVBoxLayout(info_frame)
        
        info_title = QLabel("🔒 Protection Information")
        info_title.setStyleSheet("color: #8E79E8; font-size: 16px; font-weight: bold;")
        
        info_text = QLabel(
            "To protect against data theft, consider these security measures:\n"
            "• Use strong, unique passwords for each account\n"
            "• Enable two-factor authentication where available\n"
            "• Keep your operating system and applications updated\n"
            "• Use reputable antivirus and anti-malware software\n"
            "• Be cautious of suspicious emails, links, and attachments\n"
            "• Regularly backup important data to secure locations"
        )
        info_text.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        info_text.setWordWrap(True)
        
        info_layout.addWidget(info_title)
        info_layout.addWidget(info_text)
        
        main_layout.addWidget(info_frame)
    
    def create_general_settings_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Stealer Name
        name_layout = QHBoxLayout()
        name_label = QLabel("Stealer Name:")
        name_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        name_label.setFixedWidth(150)
        
        name_input = QLineEdit()
        name_input.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        name_input.setText("ZeroTrace_Edu")
        
        name_layout.addWidget(name_label)
        name_layout.addWidget(name_input)
        
        layout.addLayout(name_layout)
        
        # Create checkboxes group
        options_group = QGroupBox("Collection Options")
        options_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        options_layout = QVBoxLayout(options_group)
        
        # Checkbox style
        checkbox_style = """
            QCheckBox {
                color: #FFFFFF;
                font-size: 14px;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                background-color: #2D2D3F;
            }
            QCheckBox::indicator:checked {
                background-color: #8E79E8;
                image: url('checkmark.png');
            }
            QCheckBox::indicator:unchecked:hover {
                background-color: #3D3D4F;
            }
        """
        
        # Create checkboxes
        options = [
            {"name": "Collect Passwords", "checked": True, "tooltip": "Simulates collecting saved passwords from browsers"},
            {"name": "Collect Cookies", "checked": True, "tooltip": "Simulates collecting browser cookies"},
            {"name": "Collect Autofill Data", "checked": True, "tooltip": "Simulates collecting browser autofill data"},
            {"name": "Collect Credit Cards", "checked": False, "tooltip": "Simulates collecting saved credit card information"},
            {"name": "Collect Crypto Wallets", "checked": True, "tooltip": "Simulates collecting cryptocurrency wallet files"},
            {"name": "Collect Screenshots", "checked": False, "tooltip": "Simulates taking screenshots of the victim's desktop"},
            {"name": "Collect System Info", "checked": True, "tooltip": "Simulates collecting system information"},
            {"name": "Collect Files", "checked": False, "tooltip": "Simulates collecting specific file types from the system"},
            {"name": "Collect Telegram", "checked": True, "tooltip": "Simulates collecting Telegram session data"},
            {"name": "Collect Discord", "checked": True, "tooltip": "Simulates collecting Discord tokens"}
        ]
        
        for option in options:
            checkbox = QCheckBox(option["name"])
            checkbox.setStyleSheet(checkbox_style)
            checkbox.setChecked(option["checked"])
            checkbox.setToolTip(option["tooltip"])
            options_layout.addWidget(checkbox)
        
        layout.addWidget(options_group)
        
        # Exfiltration Settings
        exfil_group = QGroupBox("Exfiltration Settings")
        exfil_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        exfil_layout = QFormLayout(exfil_group)
        exfil_layout.setLabelAlignment(Qt.AlignRight)
        exfil_layout.setFormAlignment(Qt.AlignLeft)
        exfil_layout.setSpacing(15)
        
        # Telegram Bot Token
        token_input = QLineEdit()
        token_input.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        token_input.setText("XXXX:YYYY_SIMULATED_TOKEN_ZZZZ")
        token_label = QLabel("Telegram Bot Token:")
        token_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        exfil_layout.addRow(token_label, token_input)
        
        # Webhook URL
        webhook_input = QLineEdit()
        webhook_input.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        webhook_input.setText("https://example.com/webhook")
        webhook_label = QLabel("Webhook URL:")
        webhook_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        exfil_layout.addRow(webhook_label, webhook_input)
        
        # FTP Settings
        ftp_server = QLineEdit()
        ftp_server.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        ftp_server.setText("ftp.example.com")
        ftp_label = QLabel("FTP Server:")
        ftp_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        exfil_layout.addRow(ftp_label, ftp_server)
        
        layout.addWidget(exfil_group)
        
        return tab
    
    def create_targets_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Browsers section
        browsers_group = QGroupBox("Target Browsers")
        browsers_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        browsers_layout = QVBoxLayout(browsers_group)
        
        # Checkbox style
        checkbox_style = """
            QCheckBox {
                color: #FFFFFF;
                font-size: 14px;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                background-color: #2D2D3F;
            }
            QCheckBox::indicator:checked {
                background-color: #8E79E8;
                image: url('checkmark.png');
            }
            QCheckBox::indicator:unchecked:hover {
                background-color: #3D3D4F;
            }
        """
        
        # Create browser checkboxes
        browsers = [
            {"name": "Google Chrome", "checked": True},
            {"name": "Mozilla Firefox", "checked": True},
            {"name": "Microsoft Edge", "checked": True},
            {"name": "Opera", "checked": True},
            {"name": "Brave", "checked": True},
            {"name": "Vivaldi", "checked": False},
            {"name": "Safari", "checked": False},
            {"name": "Yandex Browser", "checked": False}
        ]
        
        for browser in browsers:
            checkbox = QCheckBox(browser["name"])
            checkbox.setStyleSheet(checkbox_style)
            checkbox.setChecked(browser["checked"])
            browsers_layout.addWidget(checkbox)
        
        layout.addWidget(browsers_group)
        
        # Cryptocurrency wallets section
        wallets_group = QGroupBox("Target Cryptocurrency Wallets")
        wallets_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        wallets_layout = QVBoxLayout(wallets_group)
        
        # Create wallet checkboxes
        wallets = [
            {"name": "Exodus", "checked": True},
            {"name": "Electrum", "checked": True},
            {"name": "Atomic", "checked": True},
            {"name": "Binance", "checked": True},
            {"name": "MetaMask", "checked": True},
            {"name": "Coinomi", "checked": False},
            {"name": "Jaxx", "checked": False},
            {"name": "Trust Wallet", "checked": False}
        ]
        
        for wallet in wallets:
            checkbox = QCheckBox(wallet["name"])
            checkbox.setStyleSheet(checkbox_style)
            checkbox.setChecked(wallet["checked"])
            wallets_layout.addWidget(checkbox)
        
        layout.addWidget(wallets_group)
        
        # Messaging applications section
        messaging_group = QGroupBox("Target Messaging Applications")
        messaging_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        messaging_layout = QVBoxLayout(messaging_group)
        
        # Create messaging checkboxes
        messaging = [
            {"name": "Discord", "checked": True},
            {"name": "Telegram", "checked": True},
            {"name": "Skype", "checked": False},
            {"name": "Signal", "checked": False},
            {"name": "WhatsApp", "checked": False},
            {"name": "Slack", "checked": False}
        ]
        
        for app in messaging:
            checkbox = QCheckBox(app["name"])
            checkbox.setStyleSheet(checkbox_style)
            checkbox.setChecked(app["checked"])
            messaging_layout.addWidget(checkbox)
        
        layout.addWidget(messaging_group)
        
        return tab
    
    def create_advanced_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # File grabber section
        file_group = QGroupBox("File Grabber Settings")
        file_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        file_layout = QVBoxLayout(file_group)
        
        # File extensions
        extensions_label = QLabel("Target File Extensions (comma separated):")
        extensions_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        
        extensions_input = QLineEdit()
        extensions_input.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        extensions_input.setText(".txt, .pdf, .doc, .docx, .xls, .xlsx, .jpg, .png")
        
        file_layout.addWidget(extensions_label)
        file_layout.addWidget(extensions_input)
        
        # Max file size
        size_layout = QHBoxLayout()
        
        size_label = QLabel("Maximum File Size (MB):")
        size_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        
        size_input = QSpinBox()
        size_input.setStyleSheet("""
            QSpinBox {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background-color: #3D3D4F;
                border-radius: 3px;
            }
        """)
        size_input.setRange(1, 100)
        size_input.setValue(10)
        
        size_layout.addWidget(size_label)
        size_layout.addWidget(size_input)
        size_layout.addStretch()
        
        file_layout.addLayout(size_layout)
        
        # Search paths
        paths_label = QLabel("Search Paths (one per line):")
        paths_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        
        paths_input = QLineEdit()
        paths_input.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        paths_input.setText("%USERPROFILE%\\Documents, %USERPROFILE%\\Desktop")
        
        file_layout.addWidget(paths_label)
        file_layout.addWidget(paths_input)
        
        layout.addWidget(file_group)
        
        # Keylogger settings
        keylogger_group = QGroupBox("Keylogger Settings")
        keylogger_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        keylogger_layout = QVBoxLayout(keylogger_group)
        
        # Enable keylogger
        enable_keylogger = QCheckBox("Enable Keylogger (Educational Simulation)")
        enable_keylogger.setStyleSheet("""
            QCheckBox {
                color: #FFFFFF;
                font-size: 14px;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                background-color: #2D2D3F;
            }
            QCheckBox::indicator:checked {
                background-color: #8E79E8;
                image: url('checkmark.png');
            }
            QCheckBox::indicator:unchecked:hover {
                background-color: #3D3D4F;
            }
        """)
        enable_keylogger.setChecked(False)
        
        keylogger_layout.addWidget(enable_keylogger)
        
        # Log interval
        interval_layout = QHBoxLayout()
        
        interval_label = QLabel("Log Sending Interval (minutes):")
        interval_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        
        interval_input = QSpinBox()
        interval_input.setStyleSheet("""
            QSpinBox {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background-color: #3D3D4F;
                border-radius: 3px;
            }
        """)
        interval_input.setRange(1, 60)
        interval_input.setValue(15)
        
        interval_layout.addWidget(interval_label)
        interval_layout.addWidget(interval_input)
        interval_layout.addStretch()
        
        keylogger_layout.addLayout(interval_layout)
        
        layout.addWidget(keylogger_group)
        
        # Persistence settings
        persistence_group = QGroupBox("Persistence Settings")
        persistence_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        persistence_layout = QVBoxLayout(persistence_group)
        
        # Persistence methods
        methods_label = QLabel("Persistence Method:")
        methods_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        
        methods_combo = QComboBox()
        methods_combo.setStyleSheet("""
            QComboBox {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox QAbstractItemView {
                background-color: #2D2D3F;
                color: #FFFFFF;
                selection-background-color: #3D3D4F;
                selection-color: #FFFFFF;
            }
        """)
        methods_combo.addItems([
            "Startup Folder",
            "Registry Run Key",
            "Scheduled Task",
            "WMI Event Subscription",
            "Service Installation"
        ])
        
        persistence_layout.addWidget(methods_label)
        persistence_layout.addWidget(methods_combo)
        
        # Startup name
        startup_label = QLabel("Startup Name:")
        startup_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        
        startup_input = QLineEdit()
        startup_input.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        startup_input.setText("System Service")
        
        persistence_layout.addWidget(startup_label)
        persistence_layout.addWidget(startup_input)
        
        layout.addWidget(persistence_group)
        
        return tab
    
    def create_anti_detection_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Anti-VM settings
        anti_vm_group = QGroupBox("Anti-Virtual Machine")
        anti_vm_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        anti_vm_layout = QVBoxLayout(anti_vm_group)
        
        # Checkbox style
        checkbox_style = """
            QCheckBox {
                color: #FFFFFF;
                font-size: 14px;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                background-color: #2D2D3F;
            }
            QCheckBox::indicator:checked {
                background-color: #8E79E8;
                image: url('checkmark.png');
            }
            QCheckBox::indicator:unchecked:hover {
                background-color: #3D3D4F;
            }
        """
        
        # Anti-VM options
        anti_vm_options = [
            {"name": "Detect Virtual Machines", "checked": True},
            {"name": "Check for VM-related Registry Keys", "checked": True},
            {"name": "Check for VM-related Processes", "checked": True},
            {"name": "Check for VM-related Files", "checked": True},
            {"name": "Check for VM-related MAC Addresses", "checked": True}
        ]
        
        for option in anti_vm_options:
            checkbox = QCheckBox(option["name"])
            checkbox.setStyleSheet(checkbox_style)
            checkbox.setChecked(option["checked"])
            anti_vm_layout.addWidget(checkbox)
        
        layout.addWidget(anti_vm_group)
        
        # Anti-Debug settings
        anti_debug_group = QGroupBox("Anti-Debugging")
        anti_debug_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        anti_debug_layout = QVBoxLayout(anti_debug_group)
        
        # Anti-Debug options
        anti_debug_options = [
            {"name": "Detect Debugging Tools", "checked": True},
            {"name": "Check for Debugger Processes", "checked": True},
            {"name": "Use Anti-Debug API Calls", "checked": True},
            {"name": "Check for Analysis Tools", "checked": True}
        ]
        
        for option in anti_debug_options:
            checkbox = QCheckBox(option["name"])
            checkbox.setStyleSheet(checkbox_style)
            checkbox.setChecked(option["checked"])
            anti_debug_layout.addWidget(checkbox)
        
        layout.addWidget(anti_debug_group)
        
        # Anti-AV settings
        anti_av_group = QGroupBox("Anti-Antivirus")
        anti_av_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        anti_av_layout = QVBoxLayout(anti_av_group)
        
        # Anti-AV options
        anti_av_options = [
            {"name": "Detect Antivirus Products", "checked": True},
            {"name": "Use Process Hollowing", "checked": False},
            {"name": "Use DLL Injection", "checked": False},
            {"name": "Use Obfuscation", "checked": True},
            {"name": "Use Encryption", "checked": True}
        ]
        
        for option in anti_av_options:
            checkbox = QCheckBox(option["name"])
            checkbox.setStyleSheet(checkbox_style)
            checkbox.setChecked(option["checked"])
            anti_av_layout.addWidget(checkbox)
        
        layout.addWidget(anti_av_group)
        
        # Delay execution
        delay_group = QGroupBox("Execution Delay")
        delay_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        delay_layout = QVBoxLayout(delay_group)
        
        # Enable delay
        enable_delay = QCheckBox("Enable Execution Delay")
        enable_delay.setStyleSheet(checkbox_style)
        enable_delay.setChecked(True)
        
        delay_layout.addWidget(enable_delay)
        
        # Delay time
        delay_time_layout = QHBoxLayout()
        
        delay_label = QLabel("Delay Time (seconds):")
        delay_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        
        delay_input = QSpinBox()
        delay_input.setStyleSheet("""
            QSpinBox {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background-color: #3D3D4F;
                border-radius: 3px;
            }
        """)
        delay_input.setRange(0, 3600)
        delay_input.setValue(30)
        
        delay_time_layout.addWidget(delay_label)
        delay_time_layout.addWidget(delay_input)
        delay_time_layout.addStretch()
        
        delay_layout.addLayout(delay_time_layout)
        
        layout.addWidget(delay_group)
        
        return tab
    
    def save_settings(self):
        # This is a simulated function for educational purposes
        print("Settings saved (simulation)")
        
        # Show a message in the parent window's status bar if available
        if hasattr(self.parent(), "statusBar"):
            self.parent().statusBar().showMessage("Settings saved (Educational Simulation Only)", 3000)
    
    def reset_settings(self):
        # This is a simulated function for educational purposes
        print("Settings reset to defaults (simulation)")
        
        # Show a message in the parent window's status bar if available
        if hasattr(self.parent(), "statusBar"):
            self.parent().statusBar().showMessage("Settings reset to defaults (Educational Simulation Only)", 3000)
