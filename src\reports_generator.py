import os
import json
import csv
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import pandas as pd
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                            QComboBox, QDateEdit, QFileDialog, QMessageBox, QTabWidget,
                            QTableWidget, QTableWidgetItem, QHeaderView, QTextEdit,
                            QGroupBox, QFormLayout, QProgressBar)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap
from database import DatabaseManager

class ReportGenerator(QThread):
    progress_updated = pyqtSignal(int)
    report_completed = pyqtSignal(str)
    
    def __init__(self, db, report_type, start_date, end_date, output_path):
        super().__init__()
        self.db = db
        self.report_type = report_type
        self.start_date = start_date
        self.end_date = end_date
        self.output_path = output_path
    
    def run(self):
        try:
            if self.report_type == "system_performance":
                self.generate_system_performance_report()
            elif self.report_type == "security_alerts":
                self.generate_security_alerts_report()
            elif self.report_type == "network_activity":
                self.generate_network_activity_report()
            elif self.report_type == "comprehensive":
                self.generate_comprehensive_report()
            
            self.report_completed.emit("Report generated successfully!")
        except Exception as e:
            self.report_completed.emit(f"Error generating report: {str(e)}")
    
    def generate_system_performance_report(self):
        """Generate system performance report"""
        self.progress_updated.emit(10)
        
        # Get system monitoring data
        query = '''
            SELECT timestamp, cpu_percent, memory_percent, disk_percent, 
                   network_sent, network_received, active_processes
            FROM system_monitoring 
            WHERE timestamp BETWEEN ? AND ?
            ORDER BY timestamp
        '''
        data = self.db.execute_query(query, (self.start_date, self.end_date))
        
        self.progress_updated.emit(30)
        
        if not data:
            raise Exception("No data found for the selected date range")
        
        # Convert to DataFrame
        df = pd.DataFrame(data, columns=[
            'timestamp', 'cpu_percent', 'memory_percent', 'disk_percent',
            'network_sent', 'network_received', 'active_processes'
        ])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        self.progress_updated.emit(50)
        
        # Generate charts
        self.create_performance_charts(df)
        
        self.progress_updated.emit(70)
        
        # Generate CSV report
        csv_path = self.output_path.replace('.pdf', '_data.csv')
        df.to_csv(csv_path, index=False)
        
        self.progress_updated.emit(90)
        
        # Generate summary statistics
        self.generate_performance_summary(df)
        
        self.progress_updated.emit(100)
    
    def create_performance_charts(self, df):
        """Create performance charts"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('System Performance Report', fontsize=16, fontweight='bold')
        
        # CPU Usage
        axes[0, 0].plot(df['timestamp'], df['cpu_percent'], color='#FF6B6B', linewidth=2)
        axes[0, 0].set_title('CPU Usage (%)')
        axes[0, 0].set_ylabel('Percentage')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].axhline(y=80, color='red', linestyle='--', alpha=0.7, label='High Usage Threshold')
        axes[0, 0].legend()
        
        # Memory Usage
        axes[0, 1].plot(df['timestamp'], df['memory_percent'], color='#4ECDC4', linewidth=2)
        axes[0, 1].set_title('Memory Usage (%)')
        axes[0, 1].set_ylabel('Percentage')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].axhline(y=85, color='red', linestyle='--', alpha=0.7, label='High Usage Threshold')
        axes[0, 1].legend()
        
        # Disk Usage
        axes[1, 0].plot(df['timestamp'], df['disk_percent'], color='#45B7D1', linewidth=2)
        axes[1, 0].set_title('Disk Usage (%)')
        axes[1, 0].set_ylabel('Percentage')
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].axhline(y=90, color='red', linestyle='--', alpha=0.7, label='Critical Usage Threshold')
        axes[1, 0].legend()
        
        # Active Processes
        axes[1, 1].plot(df['timestamp'], df['active_processes'], color='#96CEB4', linewidth=2)
        axes[1, 1].set_title('Active Processes')
        axes[1, 1].set_ylabel('Count')
        axes[1, 1].grid(True, alpha=0.3)
        
        # Format x-axis
        for ax in axes.flat:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d %H:%M'))
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        chart_path = self.output_path.replace('.pdf', '_charts.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_performance_summary(self, df):
        """Generate performance summary"""
        summary = {
            'report_period': f"{self.start_date} to {self.end_date}",
            'total_data_points': len(df),
            'cpu_stats': {
                'average': df['cpu_percent'].mean(),
                'maximum': df['cpu_percent'].max(),
                'minimum': df['cpu_percent'].min(),
                'high_usage_incidents': len(df[df['cpu_percent'] > 80])
            },
            'memory_stats': {
                'average': df['memory_percent'].mean(),
                'maximum': df['memory_percent'].max(),
                'minimum': df['memory_percent'].min(),
                'high_usage_incidents': len(df[df['memory_percent'] > 85])
            },
            'disk_stats': {
                'average': df['disk_percent'].mean(),
                'maximum': df['disk_percent'].max(),
                'minimum': df['disk_percent'].min(),
                'critical_usage_incidents': len(df[df['disk_percent'] > 90])
            },
            'process_stats': {
                'average': df['active_processes'].mean(),
                'maximum': df['active_processes'].max(),
                'minimum': df['active_processes'].min()
            }
        }
        
        # Save summary as JSON
        summary_path = self.output_path.replace('.pdf', '_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
    
    def generate_security_alerts_report(self):
        """Generate security alerts report"""
        self.progress_updated.emit(20)
        
        query = '''
            SELECT timestamp, alert_type, severity, message, details, resolved
            FROM security_alerts 
            WHERE timestamp BETWEEN ? AND ?
            ORDER BY timestamp DESC
        '''
        data = self.db.execute_query(query, (self.start_date, self.end_date))
        
        self.progress_updated.emit(60)
        
        if not data:
            raise Exception("No security alerts found for the selected date range")
        
        # Convert to DataFrame
        df = pd.DataFrame(data, columns=[
            'timestamp', 'alert_type', 'severity', 'message', 'details', 'resolved'
        ])
        
        # Generate CSV report
        csv_path = self.output_path.replace('.pdf', '_security_alerts.csv')
        df.to_csv(csv_path, index=False)
        
        self.progress_updated.emit(80)
        
        # Generate alert statistics
        self.generate_alert_statistics(df)
        
        self.progress_updated.emit(100)
    
    def generate_alert_statistics(self, df):
        """Generate alert statistics"""
        stats = {
            'total_alerts': len(df),
            'resolved_alerts': len(df[df['resolved'] == True]),
            'unresolved_alerts': len(df[df['resolved'] == False]),
            'alerts_by_type': df['alert_type'].value_counts().to_dict(),
            'alerts_by_severity': df['severity'].value_counts().to_dict(),
            'resolution_rate': (len(df[df['resolved'] == True]) / len(df) * 100) if len(df) > 0 else 0
        }
        
        # Save statistics as JSON
        stats_path = self.output_path.replace('.pdf', '_alert_stats.json')
        with open(stats_path, 'w') as f:
            json.dump(stats, f, indent=2, default=str)

class ReportsWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = DatabaseManager()
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Title
        title_label = QLabel("Reports & Analytics")
        title_label.setStyleSheet("color: #FFFFFF; font-size: 24px; font-weight: bold;")
        layout.addWidget(title_label)
        
        # Create tabs
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #1A1A28;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #1E1E2E;
                color: #AAAAAA;
                padding: 8px 16px;
                margin-right: 4px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: #1A1A28;
                color: #FFFFFF;
            }
            QTabBar::tab:hover:!selected {
                background-color: #252535;
            }
        """)
        
        # Report Generator Tab
        generator_tab = self.create_generator_tab()
        tabs.addTab(generator_tab, "Generate Reports")
        
        # Analytics Dashboard Tab
        analytics_tab = self.create_analytics_tab()
        tabs.addTab(analytics_tab, "Analytics Dashboard")
        
        # Quick Stats Tab
        stats_tab = self.create_stats_tab()
        tabs.addTab(stats_tab, "Quick Statistics")
        
        layout.addWidget(tabs)
    
    def create_generator_tab(self):
        """Create report generator tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Report configuration group
        config_group = QGroupBox("Report Configuration")
        config_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #444;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        config_layout = QFormLayout(config_group)
        
        # Report type
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "System Performance Report",
            "Security Alerts Report", 
            "Network Activity Report",
            "Comprehensive Report"
        ])
        self.report_type_combo.setStyleSheet("""
            QComboBox {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        config_layout.addRow("Report Type:", self.report_type_combo)
        
        # Date range
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-7))
        self.start_date.setStyleSheet("""
            QDateEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        config_layout.addRow("Start Date:", self.start_date)
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setStyleSheet(self.start_date.styleSheet())
        config_layout.addRow("End Date:", self.end_date)
        
        layout.addWidget(config_group)
        
        # Generate button
        self.generate_btn = QPushButton("Generate Report")
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #8E79E8;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7A67C7;
            }
        """)
        self.generate_btn.clicked.connect(self.generate_report)
        layout.addWidget(self.generate_btn)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #444;
                border-radius: 5px;
                text-align: center;
                background-color: #2D2D3F;
                color: #FFFFFF;
            }
            QProgressBar::chunk {
                background-color: #8E79E8;
                border-radius: 5px;
            }
        """)
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #AAAAAA; font-size: 14px;")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        return tab
    
    def create_analytics_tab(self):
        """Create analytics dashboard tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Placeholder for charts
        chart_label = QLabel("Real-time Analytics Dashboard")
        chart_label.setStyleSheet("color: #FFFFFF; font-size: 18px; font-weight: bold;")
        chart_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(chart_label)
        
        # Add matplotlib canvas here for real-time charts
        self.create_analytics_charts(layout)
        
        return tab
    
    def create_analytics_charts(self, layout):
        """Create analytics charts"""
        # This would contain real-time charts using matplotlib
        placeholder = QLabel("Charts will be displayed here\n(CPU, Memory, Network usage over time)")
        placeholder.setStyleSheet("""
            QLabel {
                background-color: #1A1A28;
                color: #AAAAAA;
                border: 2px dashed #444;
                border-radius: 10px;
                padding: 50px;
                font-size: 16px;
            }
        """)
        placeholder.setAlignment(Qt.AlignCenter)
        layout.addWidget(placeholder)
    
    def create_stats_tab(self):
        """Create quick statistics tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Quick stats will be displayed here
        stats_label = QLabel("Quick System Statistics")
        stats_label.setStyleSheet("color: #FFFFFF; font-size: 18px; font-weight: bold;")
        layout.addWidget(stats_label)
        
        # Stats table
        self.stats_table = QTableWidget()
        self.stats_table.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: none;
                gridline-color: #333344;
            }
            QHeaderView::section {
                background-color: #1A1A28;
                color: #AAAAAA;
                border: none;
                padding: 8px;
                font-weight: bold;
            }
            QTableWidget::item {
                border-bottom: 1px solid #333344;
                padding: 8px;
            }
        """)
        
        self.load_quick_stats()
        layout.addWidget(self.stats_table)
        
        return tab
    
    def load_quick_stats(self):
        """Load quick statistics"""
        # This would load real statistics from the database
        stats_data = [
            ["Total System Monitoring Records", "1,234"],
            ["Total Security Alerts", "56"],
            ["Unresolved Alerts", "12"],
            ["Average CPU Usage (24h)", "45.2%"],
            ["Average Memory Usage (24h)", "67.8%"],
            ["Network Connections (Active)", "23"],
            ["Last System Scan", "2 hours ago"]
        ]
        
        self.stats_table.setRowCount(len(stats_data))
        self.stats_table.setColumnCount(2)
        self.stats_table.setHorizontalHeaderLabels(["Metric", "Value"])
        
        for row, (metric, value) in enumerate(stats_data):
            self.stats_table.setItem(row, 0, QTableWidgetItem(metric))
            self.stats_table.setItem(row, 1, QTableWidgetItem(value))
        
        # Resize columns
        header = self.stats_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        
        self.stats_table.verticalHeader().setVisible(False)
    
    def generate_report(self):
        """Generate selected report"""
        report_types = {
            0: "system_performance",
            1: "security_alerts", 
            2: "network_activity",
            3: "comprehensive"
        }
        
        report_type = report_types[self.report_type_combo.currentIndex()]
        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")
        
        # Get output path
        file_dialog = QFileDialog()
        output_path, _ = file_dialog.getSaveFileName(
            self, "Save Report", f"report_{report_type}_{start_date}_to_{end_date}.pdf",
            "PDF Files (*.pdf);;All Files (*)"
        )
        
        if output_path:
            # Show progress bar
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText("Generating report...")
            self.generate_btn.setEnabled(False)
            
            # Start report generation thread
            self.report_thread = ReportGenerator(self.db, report_type, start_date, end_date, output_path)
            self.report_thread.progress_updated.connect(self.progress_bar.setValue)
            self.report_thread.report_completed.connect(self.on_report_completed)
            self.report_thread.start()
    
    def on_report_completed(self, message):
        """Handle report completion"""
        self.progress_bar.setVisible(False)
        self.status_label.setText(message)
        self.generate_btn.setEnabled(True)
        
        if "successfully" in message:
            QMessageBox.information(self, "Success", message)
        else:
            QMessageBox.warning(self, "Error", message)
