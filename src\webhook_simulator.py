"""
Educational Webhook Simulator
This module provides educational simulation of webhook functionality
for cybersecurity learning purposes.

⚠️ EDUCATIONAL PURPOSE ONLY ⚠️
This code is designed for educational purposes to demonstrate how
malware might exfiltrate data through webhooks. No actual data
is sent to any external services.
"""

import json
import time
from datetime import datetime
from PyQt5.QtWidgets import <PERSON>Widget, QVBoxLayout, QHBoxLayout, QLabel, QTextEdit, QPushButton, QFrame
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.QtGui import QFont

class WebhookSimulator(QWidget):
    """Educational webhook simulator widget"""
    
    # Signal emitted when simulation is complete
    simulation_complete = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.simulation_timer = QTimer()
        self.simulation_timer.timeout.connect(self.update_simulation)
        self.simulation_step = 0
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Title
        title = QLabel("Educational Webhook Simulator")
        title.setStyleSheet("color: #FFFFFF; font-size: 18px; font-weight: bold;")
        layout.addWidget(title)
        
        # Warning notice
        warning_frame = QFrame()
        warning_frame.setStyleSheet("background-color: #2D2D3F; border-radius: 5px; border-left: 4px solid #FF5555;")
        warning_layout = QVBoxLayout(warning_frame)
        
        warning_title = QLabel("⚠️ Educational Simulation Only")
        warning_title.setStyleSheet("color: #FF5555; font-size: 14px; font-weight: bold;")
        
        warning_text = QLabel(
            "This simulator demonstrates how malware might exfiltrate data through webhooks. "
            "No actual data is sent to any external services. This is purely educational."
        )
        warning_text.setStyleSheet("color: #FFFFFF; font-size: 12px;")
        warning_text.setWordWrap(True)
        
        warning_layout.addWidget(warning_title)
        warning_layout.addWidget(warning_text)
        layout.addWidget(warning_frame)
        
        # Simulation log
        log_label = QLabel("Simulation Log:")
        log_label.setStyleSheet("color: #FFFFFF; font-size: 14px; font-weight: bold;")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1A1A28;
                color: #AAAAAA;
                border: none;
                border-radius: 5px;
                font-family: monospace;
                font-size: 12px;
                padding: 10px;
            }
        """)
        self.log_text.setReadOnly(True)
        self.log_text.setFixedHeight(200)
        layout.addWidget(self.log_text)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("Start Simulation")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #8E79E8;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7A67C7;
            }
            QPushButton:disabled {
                background-color: #3D3D4F;
                color: #888888;
            }
        """)
        self.start_button.clicked.connect(self.start_simulation)
        
        self.clear_button = QPushButton("Clear Log")
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #3D3D4F;
            }
        """)
        self.clear_button.clicked.connect(self.clear_log)
        
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.clear_button)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # Educational information
        info_frame = QFrame()
        info_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        info_layout = QVBoxLayout(info_frame)
        
        info_title = QLabel("🔒 How to Protect Against Webhook Exfiltration")
        info_title.setStyleSheet("color: #8E79E8; font-size: 14px; font-weight: bold;")
        
        info_text = QLabel(
            "• Monitor network traffic for suspicious outbound connections\n"
            "• Use application firewalls to block unauthorized network access\n"
            "• Implement endpoint detection and response (EDR) solutions\n"
            "• Regularly audit and monitor webhook configurations\n"
            "• Use network segmentation to limit data exfiltration\n"
            "• Educate users about phishing and social engineering attacks"
        )
        info_text.setStyleSheet("color: #FFFFFF; font-size: 12px;")
        
        info_layout.addWidget(info_title)
        info_layout.addWidget(info_text)
        layout.addWidget(info_frame)
    
    def add_log(self, message):
        """Add a message to the simulation log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
        # Auto-scroll to bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def clear_log(self):
        """Clear the simulation log"""
        self.log_text.clear()
        self.add_log("Log cleared")
    
    def start_simulation(self):
        """Start the webhook simulation"""
        self.start_button.setEnabled(False)
        self.simulation_step = 0
        
        self.add_log("=== Starting Educational Webhook Simulation ===")
        self.add_log("⚠️ This is a simulation for educational purposes only")
        self.add_log("No actual data will be sent to any external services")
        
        # Start simulation timer
        self.simulation_timer.start(1000)  # Update every second
    
    def update_simulation(self):
        """Update the simulation progress"""
        self.simulation_step += 1
        
        if self.simulation_step == 1:
            self.add_log("📡 Simulating data collection...")
            self.add_log("   • Collecting system information (simulated)")
            
        elif self.simulation_step == 2:
            self.add_log("   • Collecting browser data (simulated)")
            
        elif self.simulation_step == 3:
            self.add_log("   • Collecting network information (simulated)")
            
        elif self.simulation_step == 4:
            self.add_log("🔗 Preparing webhook payload...")
            simulated_payload = {
                "content": "🔒 Educational Simulation",
                "embeds": [{
                    "title": "Simulated Data Exfiltration",
                    "description": "This would contain stolen data in a real attack",
                    "color": 0xFF5555,
                    "fields": [
                        {"name": "System", "value": "Windows 10 (Simulated)", "inline": True},
                        {"name": "IP", "value": "************* (Simulated)", "inline": True}
                    ]
                }]
            }
            self.add_log(f"   Payload: {json.dumps(simulated_payload, indent=2)}")
            
        elif self.simulation_step == 5:
            self.add_log("🚫 SIMULATION: Would send to webhook (blocked for safety)")
            self.add_log("   URL: https://discord.com/api/webhooks/SIMULATED_URL")
            
        elif self.simulation_step == 6:
            self.add_log("✅ Simulation completed successfully")
            self.add_log("📚 Educational objective achieved: Understanding webhook exfiltration")
            self.add_log("🔒 Remember: Use this knowledge to protect against real threats")
            
            # Stop simulation
            self.simulation_timer.stop()
            self.start_button.setEnabled(True)
            self.simulation_complete.emit("Webhook simulation completed")
    
    def simulate_webhook_test(self, webhook_url):
        """Simulate testing a specific webhook URL"""
        self.clear_log()
        self.add_log(f"=== Testing Webhook (Educational Simulation) ===")
        self.add_log(f"Target URL: {webhook_url}")
        self.add_log("⚠️ No actual request will be made")
        
        # Simulate the test
        QTimer.singleShot(1000, lambda: self.add_log("🔍 Analyzing webhook URL format..."))
        QTimer.singleShot(2000, lambda: self.add_log("✅ URL format appears valid (simulation)"))
        QTimer.singleShot(3000, lambda: self.add_log("🚫 Test blocked for safety - this is educational only"))
        QTimer.singleShot(4000, lambda: self.add_log("📚 In a real scenario, this would send test data"))
