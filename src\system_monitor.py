import psutil
import platform
import socket
import threading
import time
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, Qt
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
                            QTableWidget, QTableWidgetItem, QTabWidget, QProgressBar,
                            QScrollArea, QGroupBox, QGridLayout)
from PyQt5.QtGui import QFont
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np
from database import DatabaseManager

class SystemMonitor(QObject):
    # Signals for real-time updates
    system_data_updated = pyqtSignal(dict)
    network_data_updated = pyqtSignal(list)
    alert_generated = pyqtSignal(str, str, str)  # type, severity, message

    def __init__(self):
        super().__init__()
        self.db = DatabaseManager()
        self.monitoring = False
        self.monitor_thread = None

        # Thresholds for alerts
        self.cpu_threshold = 80.0
        self.memory_threshold = 85.0
        self.disk_threshold = 90.0

        # Previous network stats for calculating rates
        self.prev_network_stats = None

        # Timer for periodic updates
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.collect_system_data)

    def start_monitoring(self, interval=5):
        """Start system monitoring"""
        if not self.monitoring:
            self.monitoring = True
            self.update_timer.start(interval * 1000)  # Convert to milliseconds
            self.monitor_thread = threading.Thread(target=self.continuous_monitoring, daemon=True)
            self.monitor_thread.start()

    def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring = False
        self.update_timer.stop()
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)

    def collect_system_data(self):
        """Collect current system data"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)

            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent

            # Network statistics
            network_stats = psutil.net_io_counters()
            network_sent = network_stats.bytes_sent
            network_received = network_stats.bytes_recv

            # Active processes
            active_processes = len(psutil.pids())

            # Create data dictionary
            system_data = {
                'timestamp': datetime.now(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'disk_percent': disk_percent,
                'network_sent': network_sent,
                'network_received': network_received,
                'active_processes': active_processes,
                'memory_total': memory.total,
                'memory_available': memory.available,
                'disk_total': disk.total,
                'disk_free': disk.free
            }

            # Store in database
            self.db.insert_system_monitoring(
                cpu_percent, memory_percent, disk_percent,
                network_sent, network_received, active_processes
            )

            # Check for alerts
            self.check_system_alerts(system_data)

            # Emit signal for UI update
            self.system_data_updated.emit(system_data)

            return system_data

        except Exception as e:
            print(f"Error collecting system data: {e}")
            return None

    def get_network_connections(self):
        """Get current network connections"""
        try:
            connections = []
            for conn in psutil.net_connections(kind='inet'):
                if conn.status == psutil.CONN_ESTABLISHED:
                    try:
                        process = psutil.Process(conn.pid) if conn.pid else None
                        process_name = process.name() if process else "Unknown"
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        process_name = "Unknown"

                    connection_data = {
                        'local_ip': conn.laddr.ip if conn.laddr else "",
                        'local_port': conn.laddr.port if conn.laddr else 0,
                        'remote_ip': conn.raddr.ip if conn.raddr else "",
                        'remote_port': conn.raddr.port if conn.raddr else 0,
                        'protocol': 'TCP' if conn.type == socket.SOCK_STREAM else 'UDP',
                        'status': conn.status,
                        'process_name': process_name,
                        'pid': conn.pid
                    }
                    connections.append(connection_data)

                    # Store in database
                    self.db.insert_network_monitoring(
                        connection_data['local_ip'],
                        connection_data['remote_ip'],
                        connection_data['local_port'],
                        connection_data['remote_port'],
                        connection_data['protocol'],
                        connection_data['status'],
                        connection_data['process_name']
                    )

            # Emit signal for UI update
            self.network_data_updated.emit(connections)
            return connections

        except Exception as e:
            print(f"Error getting network connections: {e}")
            return []

    def get_running_processes(self):
        """Get list of running processes"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    process_info = proc.info
                    process_info['memory_mb'] = proc.memory_info().rss / 1024 / 1024
                    processes.append(process_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # Sort by CPU usage
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
            return processes

        except Exception as e:
            print(f"Error getting processes: {e}")
            return []

    def check_system_alerts(self, system_data):
        """Check for system alerts based on thresholds"""
        alerts = []

        # CPU alert
        if system_data['cpu_percent'] > self.cpu_threshold:
            message = f"High CPU usage detected: {system_data['cpu_percent']:.1f}%"
            alerts.append(('CPU', 'HIGH', message))
            self.db.insert_security_alert('CPU', 'HIGH', message)

        # Memory alert
        if system_data['memory_percent'] > self.memory_threshold:
            message = f"High memory usage detected: {system_data['memory_percent']:.1f}%"
            alerts.append(('MEMORY', 'HIGH', message))
            self.db.insert_security_alert('MEMORY', 'HIGH', message)

        # Disk alert
        if system_data['disk_percent'] > self.disk_threshold:
            message = f"High disk usage detected: {system_data['disk_percent']:.1f}%"
            alerts.append(('DISK', 'CRITICAL', message))
            self.db.insert_security_alert('DISK', 'CRITICAL', message)

        # Emit alerts
        for alert_type, severity, message in alerts:
            self.alert_generated.emit(alert_type, severity, message)

    def continuous_monitoring(self):
        """Continuous monitoring in background thread"""
        while self.monitoring:
            try:
                # Collect network connections every 30 seconds
                self.get_network_connections()
                time.sleep(30)
            except Exception as e:
                print(f"Error in continuous monitoring: {e}")
                time.sleep(5)

    def get_system_info(self):
        """Get detailed system information"""
        try:
            info = {
                'platform': platform.platform(),
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'hostname': socket.gethostname(),
                'ip_address': socket.gethostbyname(socket.gethostname()),
                'boot_time': datetime.fromtimestamp(psutil.boot_time()),
                'cpu_count': psutil.cpu_count(),
                'cpu_freq': psutil.cpu_freq().current if psutil.cpu_freq() else 0
            }
            return info
        except Exception as e:
            print(f"Error getting system info: {e}")
            return {}

    def get_disk_usage(self):
        """Get disk usage for all mounted drives"""
        try:
            disk_usage = []
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_info = {
                        'device': partition.device,
                        'mountpoint': partition.mountpoint,
                        'fstype': partition.fstype,
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percent': (usage.used / usage.total) * 100
                    }
                    disk_usage.append(disk_info)
                except PermissionError:
                    continue
            return disk_usage
        except Exception as e:
            print(f"Error getting disk usage: {e}")
            return []

    def set_thresholds(self, cpu_threshold=None, memory_threshold=None, disk_threshold=None):
        """Set alert thresholds"""
        if cpu_threshold is not None:
            self.cpu_threshold = cpu_threshold
        if memory_threshold is not None:
            self.memory_threshold = memory_threshold
        if disk_threshold is not None:
            self.disk_threshold = disk_threshold


class SystemMonitorWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_app = parent
        self.system_monitor = SystemMonitor()
        self.setup_ui()

        # Connect signals
        self.system_monitor.system_data_updated.connect(self.update_system_data)
        self.system_monitor.network_data_updated.connect(self.update_network_data)

        # Start monitoring
        self.system_monitor.start_monitoring()

        # Initial data load
        self.load_initial_data()

    def setup_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Title
        title_label = QLabel("Real-time System Monitoring")
        title_label.setStyleSheet("color: #FFFFFF; font-size: 24px; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Create tabs
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #1A1A28;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #1E1E2E;
                color: #AAAAAA;
                padding: 8px 16px;
                margin-right: 4px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: #1A1A28;
                color: #FFFFFF;
            }
            QTabBar::tab:hover:!selected {
                background-color: #252535;
            }
        """)

        # System Overview Tab
        overview_tab = self.create_overview_tab()
        tabs.addTab(overview_tab, "System Overview")

        # Processes Tab
        processes_tab = self.create_processes_tab()
        tabs.addTab(processes_tab, "Processes")

        # Network Tab
        network_tab = self.create_network_tab()
        tabs.addTab(network_tab, "Network")

        # Performance Tab
        performance_tab = self.create_performance_tab()
        tabs.addTab(performance_tab, "Performance")

        main_layout.addWidget(tabs)

    def create_overview_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # System stats grid
        stats_frame = QFrame()
        stats_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        stats_layout = QGridLayout(stats_frame)
        stats_layout.setContentsMargins(20, 20, 20, 20)
        stats_layout.setSpacing(20)

        # CPU Usage
        self.cpu_progress = self.create_progress_widget("CPU Usage", 0)
        stats_layout.addWidget(self.cpu_progress, 0, 0)

        # Memory Usage
        self.memory_progress = self.create_progress_widget("Memory Usage", 0)
        stats_layout.addWidget(self.memory_progress, 0, 1)

        # Disk Usage
        self.disk_progress = self.create_progress_widget("Disk Usage", 0)
        stats_layout.addWidget(self.disk_progress, 1, 0)

        # Active Processes
        self.processes_label = self.create_info_widget("Active Processes", "0")
        stats_layout.addWidget(self.processes_label, 1, 1)

        layout.addWidget(stats_frame)

        # System Information
        info_group = QGroupBox("System Information")
        info_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
                background-color: #1A1A28;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)

        info_layout = QVBoxLayout(info_group)
        self.system_info_label = QLabel("Loading system information...")
        self.system_info_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        self.system_info_label.setWordWrap(True)
        info_layout.addWidget(self.system_info_label)

        layout.addWidget(info_group)

        return tab

    def create_progress_widget(self, title, value):
        widget = QFrame()
        widget.setStyleSheet("background-color: #2D2D3F; border-radius: 5px;")
        widget.setMinimumHeight(120)

        layout = QVBoxLayout(widget)
        layout.setContentsMargins(15, 15, 15, 15)

        title_label = QLabel(title)
        title_label.setStyleSheet("color: #AAAAAA; font-size: 14px;")

        progress = QProgressBar()
        progress.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 5px;
                background-color: #1A1A28;
                text-align: center;
                color: #FFFFFF;
                font-size: 14px;
                font-weight: bold;
            }
            QProgressBar::chunk {
                border-radius: 5px;
                background-color: #8E79E8;
            }
        """)
        progress.setValue(value)

        value_label = QLabel(f"{value}%")
        value_label.setStyleSheet("color: #FFFFFF; font-size: 18px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)

        layout.addWidget(title_label)
        layout.addWidget(progress)
        layout.addWidget(value_label)

        # Store references for updates
        widget.progress = progress
        widget.value_label = value_label

        return widget

    def create_info_widget(self, title, value):
        widget = QFrame()
        widget.setStyleSheet("background-color: #2D2D3F; border-radius: 5px;")
        widget.setMinimumHeight(120)

        layout = QVBoxLayout(widget)
        layout.setContentsMargins(15, 15, 15, 15)

        title_label = QLabel(title)
        title_label.setStyleSheet("color: #AAAAAA; font-size: 14px;")

        value_label = QLabel(value)
        value_label.setStyleSheet("color: #FFFFFF; font-size: 24px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)

        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addStretch()

        # Store reference for updates
        widget.value_label = value_label

        return widget

    def create_processes_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # Processes table
        self.processes_table = QTableWidget()
        self.processes_table.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                gridline-color: #333344;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333344;
            }
            QTableWidget::item:selected {
                background-color: #8E79E8;
            }
            QHeaderView::section {
                background-color: #2D2D3F;
                color: #FFFFFF;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        # Set table headers
        headers = ["PID", "Name", "CPU %", "Memory %", "Memory (MB)", "Status"]
        self.processes_table.setColumnCount(len(headers))
        self.processes_table.setHorizontalHeaderLabels(headers)

        layout.addWidget(self.processes_table)

        return tab

    def create_network_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # Network connections table
        self.network_table = QTableWidget()
        self.network_table.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                gridline-color: #333344;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333344;
            }
            QTableWidget::item:selected {
                background-color: #8E79E8;
            }
            QHeaderView::section {
                background-color: #2D2D3F;
                color: #FFFFFF;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        # Set table headers
        headers = ["Local IP", "Local Port", "Remote IP", "Remote Port", "Protocol", "Status", "Process"]
        self.network_table.setColumnCount(len(headers))
        self.network_table.setHorizontalHeaderLabels(headers)

        layout.addWidget(self.network_table)

        return tab

    def create_performance_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # Performance charts placeholder
        performance_label = QLabel("Performance Charts")
        performance_label.setStyleSheet("color: #FFFFFF; font-size: 18px; font-weight: bold;")
        layout.addWidget(performance_label)

        # Disk usage information
        disk_group = QGroupBox("Disk Usage")
        disk_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
                background-color: #1A1A28;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)

        disk_layout = QVBoxLayout(disk_group)
        self.disk_info_label = QLabel("Loading disk information...")
        self.disk_info_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        self.disk_info_label.setWordWrap(True)
        disk_layout.addWidget(self.disk_info_label)

        layout.addWidget(disk_group)

        return tab

    def load_initial_data(self):
        """Load initial system data"""
        # Load system information
        system_info = self.system_monitor.get_system_info()
        if system_info:
            info_text = f"""
Platform: {system_info.get('platform', 'Unknown')}
System: {system_info.get('system', 'Unknown')}
Release: {system_info.get('release', 'Unknown')}
Machine: {system_info.get('machine', 'Unknown')}
Processor: {system_info.get('processor', 'Unknown')}
Hostname: {system_info.get('hostname', 'Unknown')}
IP Address: {system_info.get('ip_address', 'Unknown')}
Boot Time: {system_info.get('boot_time', 'Unknown')}
CPU Count: {system_info.get('cpu_count', 'Unknown')}
CPU Frequency: {system_info.get('cpu_freq', 0):.2f} MHz
            """.strip()
            self.system_info_label.setText(info_text)

        # Load disk information
        disk_usage = self.system_monitor.get_disk_usage()
        if disk_usage:
            disk_text = ""
            for disk in disk_usage:
                disk_text += f"""
Device: {disk['device']}
Mount Point: {disk['mountpoint']}
File System: {disk['fstype']}
Total: {disk['total'] / (1024**3):.2f} GB
Used: {disk['used'] / (1024**3):.2f} GB
Free: {disk['free'] / (1024**3):.2f} GB
Usage: {disk['percent']:.1f}%

"""
            self.disk_info_label.setText(disk_text.strip())

        # Load processes
        self.update_processes_table()

    def update_system_data(self, data):
        """Update system data displays"""
        # Update CPU
        cpu_percent = int(data.get('cpu_percent', 0))
        self.cpu_progress.progress.setValue(cpu_percent)
        self.cpu_progress.value_label.setText(f"{cpu_percent}%")

        # Update Memory
        memory_percent = int(data.get('memory_percent', 0))
        self.memory_progress.progress.setValue(memory_percent)
        self.memory_progress.value_label.setText(f"{memory_percent}%")

        # Update Disk
        disk_percent = int(data.get('disk_percent', 0))
        self.disk_progress.progress.setValue(disk_percent)
        self.disk_progress.value_label.setText(f"{disk_percent}%")

        # Update Active Processes
        active_processes = data.get('active_processes', 0)
        self.processes_label.value_label.setText(str(active_processes))

    def update_network_data(self, connections):
        """Update network connections table"""
        self.network_table.setRowCount(len(connections))

        for row, conn in enumerate(connections):
            self.network_table.setItem(row, 0, QTableWidgetItem(conn.get('local_ip', '')))
            self.network_table.setItem(row, 1, QTableWidgetItem(str(conn.get('local_port', ''))))
            self.network_table.setItem(row, 2, QTableWidgetItem(conn.get('remote_ip', '')))
            self.network_table.setItem(row, 3, QTableWidgetItem(str(conn.get('remote_port', ''))))
            self.network_table.setItem(row, 4, QTableWidgetItem(conn.get('protocol', '')))
            self.network_table.setItem(row, 5, QTableWidgetItem(conn.get('status', '')))
            self.network_table.setItem(row, 6, QTableWidgetItem(conn.get('process_name', '')))

    def update_processes_table(self):
        """Update processes table"""
        processes = self.system_monitor.get_running_processes()
        self.processes_table.setRowCount(len(processes[:50]))  # Show top 50 processes

        for row, proc in enumerate(processes[:50]):
            self.processes_table.setItem(row, 0, QTableWidgetItem(str(proc.get('pid', ''))))
            self.processes_table.setItem(row, 1, QTableWidgetItem(proc.get('name', '')))
            self.processes_table.setItem(row, 2, QTableWidgetItem(f"{proc.get('cpu_percent', 0):.1f}"))
            self.processes_table.setItem(row, 3, QTableWidgetItem(f"{proc.get('memory_percent', 0):.1f}"))
            self.processes_table.setItem(row, 4, QTableWidgetItem(f"{proc.get('memory_mb', 0):.1f}"))
            self.processes_table.setItem(row, 5, QTableWidgetItem(proc.get('status', '')))

    def closeEvent(self, event):
        """Handle widget close event"""
        if hasattr(self, 'system_monitor'):
            self.system_monitor.stop_monitoring()
        event.accept()