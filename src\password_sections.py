import os
import j<PERSON>
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QTableWidget, QTableWidgetItem, QHeaderView
from PyQt5.QtGui import QColor, QPixmap, QIcon
from PyQt5.QtCore import Qt

class PasswordSectionsWidget(QWidget):
    def __init__(self, dashboard_components, parent=None):
        super().__init__(parent)
        self.dashboard = dashboard_components
        self.setup_ui()
    
    def setup_ui(self):
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(15)
        
        # Create password sections title
        title_label = QLabel("Password & Files")
        title_label.setStyleSheet("color: #FFFFFF; font-size: 16px; font-weight: bold;")
        main_layout.addWidget(title_label)
        
        # Create password panels layout
        passwords_layout = QHBoxLayout()
        passwords_layout.setSpacing(15)
        
        # Password types with their icons and colors
        password_types = [
            {"name": "FileZilla", "icon": "📁", "color": "#e74c3c"},
            {"name": "Chrome Passwords", "icon": "🌐", "color": "#3498db"},
            {"name": "Edge Passwords", "icon": "🔍", "color": "#2ecc71"},
            {"name": "Received Files", "icon": "📄", "color": "#9b59b6"}
        ]
        
        # Create password panels
        self.password_panels = {}
        for password in password_types:
            password_panel = self.create_password_panel(
                password["name"], 
                password["icon"], 
                password["color"]
            )
            self.password_panels[password["name"]] = password_panel
            passwords_layout.addWidget(password_panel)
        
        main_layout.addLayout(passwords_layout)
        
        # Add password details section
        details_frame = QFrame()
        details_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px; margin-top: 10px;")
        details_layout = QVBoxLayout(details_frame)
        
        details_title = QLabel("Password Details")
        details_title.setStyleSheet("color: #FFFFFF; font-size: 16px; font-weight: bold;")
        details_layout.addWidget(details_title)
        
        # Add password table
        self.password_table = PasswordTable()
        details_layout.addWidget(self.password_table)
        
        main_layout.addWidget(details_frame)
        
        # Add educational note
        note_label = QLabel("Note: This is a simulated password manager for educational purposes only. No real passwords are collected or stored.")
        note_label.setStyleSheet("color: #AAAAAA; font-size: 12px; font-style: italic;")
        main_layout.addWidget(note_label)
        
        # Initial update
        self.update_data()
    
    def create_password_panel(self, password_name, icon, color):
        panel = QFrame()
        panel.setStyleSheet(f"background-color: #1A1A28; border-radius: 5px;")
        panel.setMinimumHeight(120)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Header with icon and name
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"color: {color}; font-size: 24px;")
        
        name_label = QLabel(password_name)
        name_label.setStyleSheet(f"color: #FFFFFF; font-size: 16px; font-weight: bold;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(name_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Title
        title_label = QLabel(f"Total {password_name} Log")
        title_label.setStyleSheet("color: #AAAAAA; font-size: 14px;")
        layout.addWidget(title_label)
        
        # Value
        value_label = QLabel("0")
        value_label.setStyleSheet(f"color: {color}; font-size: 24px; font-weight: bold;")
        panel.value_label = value_label  # Store reference for updates
        layout.addWidget(value_label)
        
        # Subtitle
        subtitle_label = QLabel("During all this time")
        subtitle_label.setStyleSheet("color: #AAAAAA; font-size: 12px;")
        layout.addWidget(subtitle_label)
        
        return panel
    
    def update_data(self):
        """Update all password data in the UI"""
        # Update password panels
        for password, panel in self.password_panels.items():
            panel.value_label.setText(str(self.dashboard.password_data[password]))
        
        # Update password table with simulated data
        self.password_table.update_data(self.generate_sample_password_data())
    
    def generate_sample_password_data(self):
        """Generate sample password data for educational purposes"""
        sample_data = []
        
        # Sample websites and usernames
        websites = [
            "example.com", "demo-site.org", "test-platform.net", 
            "sample-mail.com", "learning-portal.edu"
        ]
        
        usernames = [
            "user123", "testuser", "demo_account", 
            "sample_user", "education_demo"
        ]
        
        # Generate sample entries
        import random
        
        for i in range(10):
            entry = {
                "type": random.choice(list(self.dashboard.password_data.keys())),
                "website": random.choice(websites),
                "username": random.choice(usernames),
                "date": f"2025-{random.randint(1, 12):02d}-{random.randint(1, 28):02d}"
            }
            sample_data.append(entry)
        
        return sample_data

class PasswordTable(QTableWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: none;
                gridline-color: #333344;
            }
            QHeaderView::section {
                background-color: #1A1A28;
                color: #AAAAAA;
                border: none;
                padding: 5px;
            }
            QTableWidget::item {
                border-bottom: 1px solid #333344;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #2D2D3F;
            }
        """)
        
        # Set up headers
        self.setColumnCount(4)
        self.setHorizontalHeaderLabels(["Type", "Website", "Username", "Date"])
        
        # Set column widths
        header = self.horizontalHeader()
        for i in range(4):
            header.setSectionResizeMode(i, QHeaderView.Stretch)
        
        # No vertical header
        self.verticalHeader().setVisible(False)
        
        # Selection settings
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        
        # No editing
        self.setEditTriggers(QTableWidget.NoEditTriggers)
        
        # Set row height
        self.verticalHeader().setDefaultSectionSize(40)
    
    def update_data(self, password_data):
        """Update table with password data"""
        self.setRowCount(0)  # Clear existing rows
        
        for row, entry in enumerate(password_data):
            self.insertRow(row)
            
            # Add data to cells
            type_item = QTableWidgetItem(entry["type"])
            type_item.setForeground(QColor("#FFFFFF"))
            self.setItem(row, 0, type_item)
            
            website_item = QTableWidgetItem(entry["website"])
            website_item.setForeground(QColor("#3498db"))
            self.setItem(row, 1, website_item)
            
            username_item = QTableWidgetItem(entry["username"])
            username_item.setForeground(QColor("#AAAAAA"))
            self.setItem(row, 2, username_item)
            
            date_item = QTableWidgetItem(entry["date"])
            date_item.setForeground(QColor("#AAAAAA"))
            self.setItem(row, 3, date_item)
