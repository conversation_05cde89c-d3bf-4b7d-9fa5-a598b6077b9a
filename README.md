# نظام مراقبة الأمان السيبراني (Cybersecurity Monitoring System)

⚠️ **تنبيه تعليمي مهم** ⚠️

هذا المشروع مصمم **للأغراض التعليمية فقط** لتوضيح مفاهيم مراقبة الأمان السيبراني وتقنيات تحليل البرمجيات الخبيثة. أي وظائف "stealer" أو "builder" هي محاكاة تعليمية بحتة ولا تنشئ برمجيات خبيثة حقيقية أو تقوم بأنشطة ضارة.

## المميزات الحقيقية

- **مراقبة النظام في الوقت الفعلي**: مراقبة استخدام CPU, RAM, القرص الصلب والشبكة
- **نظام إدارة كلمات المرور**: تخزين آمن ومشفر لكلمات المرور مع توليد كلمات مرور قوية
- **مراقبة الشبكة**: تحليل حركة الشبكة وكشف الأنشطة المشبوهة
- **تحليل ملفات السجل**: قراءة وتحليل سجلات النظام الحقيقية
- **نظام التنبيهات**: إشعارات فورية للأحداث المهمة
- **تقارير مفصلة**: تصدير التقارير بصيغ مختلفة (PDF, Excel, CSV)
- **قاعدة بيانات متقدمة**: استخدام SQLite لتخزين البيانات بكفاءة
- **واجهة مستخدم متطورة**: تصميم حديث مع رسوم بيانية تفاعلية
- **نظام أمان متقدم**: تشفير البيانات ونظام مصادقة

## المميزات التعليمية (محاكاة فقط)

- **محاكاة إعدادات Stealer**: واجهة تعليمية توضح كيف تبدو إعدادات البرمجيات الخبيثة
- **محاكاة Builder**: واجهة تعليمية توضح كيف تبدو أدوات إنشاء البرمجيات الخبيثة
- **تحليل التهديدات**: فهم كيفية عمل البرمجيات الخبيثة للحماية منها

## متطلبات النظام

- Python 3.8 أو أحدث
- PyQt5
- SQLite3
- psutil (لمراقبة النظام)
- cryptography (للتشفير)
- matplotlib (للرسوم البيانية)
- pandas (لتحليل البيانات)
- requests (للاتصال بالشبكة)

## التثبيت

### المتطلبات الأساسية
- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux
- ذاكرة الوصول العشوائي: 2GB على الأقل
- مساحة القرص الصلب: 500MB

### خطوات التثبيت

1. **تثبيت Python**
   قم بتثبيت Python من [python.org](https://www.python.org/downloads/)

2. **تنزيل المشروع**
   ```bash
   git clone <repository-url>
   cd dashboard_app
   ```

3. **تثبيت المتطلبات**
   ```bash
   pip install -r requirements.txt
   ```

   أو تثبيت المكتبات يدوياً:
   ```bash
   pip install PyQt5 psutil cryptography matplotlib pandas requests numpy
   ```

## كيفية التشغيل

### الطريقة المفضلة (مع معالجة الأخطاء)
```bash
python run_app.py
```

### التشغيل السريع (للاختبار)
```bash
python quick_start.py
```

### الطريقة البديلة
```bash
python src/main.py
```

### تشغيل مع صلاحيات المدير (للوصول الكامل لمراقبة النظام)
```bash
# Windows
python run_app.py

# Linux/macOS
sudo python run_app.py
```

## ⚠️ تحذيرات أمنية مهمة

### للمطورين والمستخدمين:
- **لا تضع webhook URLs حقيقية** في الكود أو الإعدادات
- **لا تشارك معلومات حساسة** مثل Discord webhooks أو API keys
- **استخدم البرنامج للأغراض التعليمية فقط**
- **لا تقم بتعديل الكود لإرسال بيانات حقيقية**

### حماية Discord Webhooks:
- احتفظ بـ webhook URLs كسر
- لا تشاركها في repositories عامة
- استخدم متغيرات البيئة للمعلومات الحساسة
- راقب استخدام webhooks بانتظام

## هيكل المشروع

```
cybersecurity_monitoring_system/
├── run_app.py                     # نقطة الدخول الرئيسية (مع معالجة الأخطاء)
├── requirements.txt               # متطلبات المشروع
├── README.md                      # ملف القراءة (هذا الملف)
├── src/                          # كود المصدر
│   ├── main.py                   # التطبيق الرئيسي
│   ├── database.py               # إدارة قاعدة البيانات
│   ├── system_monitor.py         # مراقبة النظام في الوقت الفعلي
│   ├── password_manager.py       # إدارة كلمات المرور المشفرة
│   ├── network_monitor.py        # مراقبة الشبكة والاتصالات
│   ├── security_alerts.py        # نظام التنبيهات الأمنية
│   ├── reports_generator.py      # مولد التقارير والتحليلات
│   ├── dashboard_components.py   # مكونات لوحة المعلومات
│   └── country_logs.py          # عرض سجلات البلدان
├── data/                        # قاعدة البيانات والبيانات المحفوظة
│   ├── cybersecurity.db         # قاعدة بيانات SQLite
│   └── encryption.key           # مفتاح التشفير
├── logs/                        # ملفات السجل
├── reports/                     # التقارير المُنتجة
└── backups/                     # النسخ الاحتياطية
```

## الاستخدام

### 1. لوحة المعلومات الرئيسية
- عرض إحصائيات النظام في الوقت الفعلي
- مراقبة استخدام المعالج والذاكرة والقرص الصلب
- عرض الاتصالات النشطة والعمليات الجارية

### 2. مراقب النظام
- مراقبة الأداء في الوقت الفعلي
- تتبع استخدام الموارد
- إنشاء تنبيهات عند تجاوز الحدود المحددة

### 3. إدارة كلمات المرور
- تخزين آمن ومشفر لكلمات المرور
- مولد كلمات مرور قوية
- تصنيف وبحث في كلمات المرور
- فحص قوة كلمات المرور

### 4. مراقب الشبكة
- عرض الاتصالات النشطة
- فحص الشبكة المحلية
- فحص المنافذ المفتوحة
- مراقبة حركة البيانات

### 5. التنبيهات الأمنية
- كشف التهديدات في الوقت الفعلي
- تنبيهات الاستخدام المرتفع للموارد
- مراقبة الأنشطة المشبوهة
- إدارة وحل التنبيهات

### 6. التقارير والتحليلات
- إنشاء تقارير مفصلة
- تصدير البيانات بصيغ مختلفة
- تحليل الأداء التاريخي
- رسوم بيانية تفاعلية

## الأمان والخصوصية

- **التشفير**: جميع البيانات الحساسة مشفرة باستخدام مكتبة cryptography
- **قاعدة البيانات المحلية**: البيانات تُحفظ محلياً في قاعدة بيانات SQLite
- **عدم الاتصال بالإنترنت**: التطبيق يعمل بالكامل دون الحاجة للاتصال بالإنترنت
- **صلاحيات محدودة**: يطلب فقط الصلاحيات اللازمة لمراقبة النظام

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ في تثبيت المتطلبات**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

2. **مشاكل الصلاحيات**
   - تشغيل التطبيق كمدير في Windows
   - استخدام sudo في Linux/macOS

3. **خطأ في قاعدة البيانات**
   - حذف مجلد data والسماح للتطبيق بإعادة إنشائه

4. **مشاكل في واجهة المستخدم**
   - التأكد من تثبيت PyQt5 بشكل صحيح
   - إعادة تشغيل التطبيق

## المساهمة في المشروع

نرحب بالمساهمات! يمكنك:
- الإبلاغ عن الأخطاء
- اقتراح ميزات جديدة
- تحسين الكود الموجود
- إضافة ترجمات

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للمزيد من التفاصيل.

## الدعم

للحصول على الدعم:
- افتح issue في GitHub
- راجع ملفات السجل في مجلد logs/
- تحقق من متطلبات النظام

---

**تحذير**: هذا التطبيق مخصص لأغراض المراقبة المشروعة فقط. استخدمه بمسؤولية وفقاً للقوانين المحلية.
