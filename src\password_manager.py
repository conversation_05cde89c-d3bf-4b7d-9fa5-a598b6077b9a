import secrets
import string
import hashlib
import re
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
                            QDialog, QFormLayout, QTextEdit, QComboBox, QMessageBox,
                            QCheckBox, QSpinBox, QGroupBox, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QColor, QFont
from database import DatabaseManager

class PasswordGenerator:
    @staticmethod
    def generate_password(length=12, use_uppercase=True, use_lowercase=True,
                         use_numbers=True, use_symbols=True, exclude_ambiguous=True):
        """Generate a secure password with specified criteria"""
        characters = ""

        if use_lowercase:
            chars = string.ascii_lowercase
            if exclude_ambiguous:
                chars = chars.replace('l', '').replace('o', '')
            characters += chars

        if use_uppercase:
            chars = string.ascii_uppercase
            if exclude_ambiguous:
                chars = chars.replace('I', '').replace('O', '')
            characters += chars

        if use_numbers:
            chars = string.digits
            if exclude_ambiguous:
                chars = chars.replace('0', '').replace('1', '')
            characters += chars

        if use_symbols:
            chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            characters += chars

        if not characters:
            characters = string.ascii_letters + string.digits

        # Ensure password has at least one character from each selected type
        password = []
        if use_lowercase:
            password.append(secrets.choice(string.ascii_lowercase))
        if use_uppercase:
            password.append(secrets.choice(string.ascii_uppercase))
        if use_numbers:
            password.append(secrets.choice(string.digits))
        if use_symbols:
            password.append(secrets.choice("!@#$%^&*()_+-=[]{}|;:,.<>?"))

        # Fill the rest randomly
        for _ in range(length - len(password)):
            password.append(secrets.choice(characters))

        # Shuffle the password
        secrets.SystemRandom().shuffle(password)
        return ''.join(password)

    @staticmethod
    def check_password_strength(password):
        """Check password strength and return score and feedback"""
        score = 0
        feedback = []

        # Length check
        if len(password) >= 12:
            score += 25
        elif len(password) >= 8:
            score += 15
            feedback.append("Consider using a longer password (12+ characters)")
        else:
            feedback.append("Password is too short (minimum 8 characters)")

        # Character variety checks
        if re.search(r'[a-z]', password):
            score += 15
        else:
            feedback.append("Add lowercase letters")

        if re.search(r'[A-Z]', password):
            score += 15
        else:
            feedback.append("Add uppercase letters")

        if re.search(r'\d', password):
            score += 15
        else:
            feedback.append("Add numbers")

        if re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]', password):
            score += 20
        else:
            feedback.append("Add special characters")

        # Bonus for no repeated characters
        if len(set(password)) == len(password):
            score += 10
        else:
            feedback.append("Avoid repeated characters")

        # Determine strength level
        if score >= 90:
            strength = "Very Strong"
            color = "#00FF00"
        elif score >= 70:
            strength = "Strong"
            color = "#90EE90"
        elif score >= 50:
            strength = "Medium"
            color = "#FFD700"
        elif score >= 30:
            strength = "Weak"
            color = "#FFA500"
        else:
            strength = "Very Weak"
            color = "#FF0000"

        return {
            'score': score,
            'strength': strength,
            'color': color,
            'feedback': feedback
        }

class PasswordDialog(QDialog):
    def __init__(self, parent=None, password_data=None):
        super().__init__(parent)
        self.password_data = password_data
        self.setup_ui()

        if password_data:
            self.load_password_data()

    def setup_ui(self):
        self.setWindowTitle("Add/Edit Password")
        self.setModal(True)
        self.resize(500, 600)

        layout = QVBoxLayout(self)

        # Form layout
        form_layout = QFormLayout()

        # Title
        self.title_edit = QLineEdit()
        self.title_edit.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        form_layout.addRow("Title:", self.title_edit)

        # Username
        self.username_edit = QLineEdit()
        self.username_edit.setStyleSheet(self.title_edit.styleSheet())
        form_layout.addRow("Username:", self.username_edit)

        # Password with generator
        password_layout = QHBoxLayout()
        self.password_edit = QLineEdit()
        self.password_edit.setStyleSheet(self.title_edit.styleSheet())
        self.password_edit.textChanged.connect(self.update_password_strength)

        self.generate_btn = QPushButton("Generate")
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #8E79E8;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7A67C7;
            }
        """)
        self.generate_btn.clicked.connect(self.show_password_generator)

        password_layout.addWidget(self.password_edit)
        password_layout.addWidget(self.generate_btn)
        form_layout.addRow("Password:", password_layout)

        # Password strength indicator
        self.strength_label = QLabel("Password Strength: Not Set")
        self.strength_label.setStyleSheet("color: #AAAAAA; font-size: 12px;")
        self.strength_progress = QProgressBar()
        self.strength_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #444;
                border-radius: 5px;
                text-align: center;
                background-color: #2D2D3F;
            }
            QProgressBar::chunk {
                border-radius: 5px;
            }
        """)
        form_layout.addRow(self.strength_label, self.strength_progress)

        # URL
        self.url_edit = QLineEdit()
        self.url_edit.setStyleSheet(self.title_edit.styleSheet())
        form_layout.addRow("URL:", self.url_edit)

        # Category
        self.category_combo = QComboBox()
        self.category_combo.addItems(["General", "Work", "Personal", "Banking", "Social", "Shopping", "Other"])
        self.category_combo.setStyleSheet("""
            QComboBox {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #FFFFFF;
            }
        """)
        form_layout.addRow("Category:", self.category_combo)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("Notes:", self.notes_edit)

        layout.addLayout(form_layout)

        # Buttons
        button_layout = QHBoxLayout()

        self.save_btn = QPushButton("Save")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #00AA00;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #008800;
            }
        """)
        self.save_btn.clicked.connect(self.accept)

        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #AA0000;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #880000;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)

    def update_password_strength(self):
        """Update password strength indicator"""
        password = self.password_edit.text()
        if not password:
            self.strength_label.setText("Password Strength: Not Set")
            self.strength_progress.setValue(0)
            return

        strength_info = PasswordGenerator.check_password_strength(password)
        self.strength_label.setText(f"Password Strength: {strength_info['strength']}")
        self.strength_progress.setValue(strength_info['score'])

        # Update progress bar color
        self.strength_progress.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid #444;
                border-radius: 5px;
                text-align: center;
                background-color: #2D2D3F;
                color: #FFFFFF;
            }}
            QProgressBar::chunk {{
                background-color: {strength_info['color']};
                border-radius: 5px;
            }}
        """)

    def show_password_generator(self):
        """Show password generator dialog"""
        generator_dialog = PasswordGeneratorDialog(self)
        if generator_dialog.exec_() == QDialog.Accepted:
            self.password_edit.setText(generator_dialog.generated_password)

    def load_password_data(self):
        """Load existing password data"""
        if self.password_data:
            self.title_edit.setText(self.password_data[1])
            self.username_edit.setText(self.password_data[2] or "")
            self.password_edit.setText(self.password_data[3] or "")
            self.url_edit.setText(self.password_data[4] or "")
            self.notes_edit.setText(self.password_data[5] or "")

            # Set category
            category = self.password_data[7] if len(self.password_data) > 7 else "General"
            index = self.category_combo.findText(category)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)

    def get_password_data(self):
        """Get password data from form"""
        return {
            'title': self.title_edit.text(),
            'username': self.username_edit.text(),
            'password': self.password_edit.text(),
            'url': self.url_edit.text(),
            'notes': self.notes_edit.toPlainText(),
            'category': self.category_combo.currentText()
        }

class PasswordGeneratorDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.generated_password = ""
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Password Generator")
        self.setModal(True)
        self.resize(400, 500)

        layout = QVBoxLayout(self)

        # Options group
        options_group = QGroupBox("Password Options")
        options_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #444;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)

        options_layout = QVBoxLayout(options_group)

        # Length
        length_layout = QHBoxLayout()
        length_label = QLabel("Length:")
        length_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        self.length_spin = QSpinBox()
        self.length_spin.setRange(4, 128)
        self.length_spin.setValue(12)
        self.length_spin.setStyleSheet("""
            QSpinBox {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 5px;
                font-size: 14px;
            }
        """)
        length_layout.addWidget(length_label)
        length_layout.addWidget(self.length_spin)
        length_layout.addStretch()
        options_layout.addLayout(length_layout)

        # Checkboxes
        checkbox_style = """
            QCheckBox {
                color: #FFFFFF;
                font-size: 14px;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                background-color: #2D2D3F;
                border: 1px solid #444;
            }
            QCheckBox::indicator:checked {
                background-color: #8E79E8;
            }
        """

        self.uppercase_check = QCheckBox("Include Uppercase Letters (A-Z)")
        self.uppercase_check.setChecked(True)
        self.uppercase_check.setStyleSheet(checkbox_style)
        options_layout.addWidget(self.uppercase_check)

        self.lowercase_check = QCheckBox("Include Lowercase Letters (a-z)")
        self.lowercase_check.setChecked(True)
        self.lowercase_check.setStyleSheet(checkbox_style)
        options_layout.addWidget(self.lowercase_check)

        self.numbers_check = QCheckBox("Include Numbers (0-9)")
        self.numbers_check.setChecked(True)
        self.numbers_check.setStyleSheet(checkbox_style)
        options_layout.addWidget(self.numbers_check)

        self.symbols_check = QCheckBox("Include Symbols (!@#$%^&*)")
        self.symbols_check.setChecked(True)
        self.symbols_check.setStyleSheet(checkbox_style)
        options_layout.addWidget(self.symbols_check)

        self.exclude_ambiguous_check = QCheckBox("Exclude Ambiguous Characters (0, O, l, I)")
        self.exclude_ambiguous_check.setChecked(True)
        self.exclude_ambiguous_check.setStyleSheet(checkbox_style)
        options_layout.addWidget(self.exclude_ambiguous_check)

        layout.addWidget(options_group)

        # Generate button
        self.generate_btn = QPushButton("Generate Password")
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #8E79E8;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 12px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7A67C7;
            }
        """)
        self.generate_btn.clicked.connect(self.generate_password)
        layout.addWidget(self.generate_btn)

        # Generated password display
        self.password_display = QLineEdit()
        self.password_display.setReadOnly(True)
        self.password_display.setStyleSheet("""
            QLineEdit {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: 2px solid #8E79E8;
                border-radius: 5px;
                padding: 12px;
                font-size: 16px;
                font-family: 'Courier New', monospace;
            }
        """)
        layout.addWidget(self.password_display)

        # Strength indicator
        self.strength_label = QLabel("Password Strength: Not Generated")
        self.strength_label.setStyleSheet("color: #AAAAAA; font-size: 12px;")
        self.strength_progress = QProgressBar()
        self.strength_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #444;
                border-radius: 5px;
                text-align: center;
                background-color: #2D2D3F;
            }
            QProgressBar::chunk {
                border-radius: 5px;
            }
        """)
        layout.addWidget(self.strength_label)
        layout.addWidget(self.strength_progress)

        # Buttons
        button_layout = QHBoxLayout()

        self.copy_btn = QPushButton("Copy to Clipboard")
        self.copy_btn.setStyleSheet("""
            QPushButton {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #3D3D4F;
            }
        """)
        self.copy_btn.clicked.connect(self.copy_to_clipboard)
        self.copy_btn.setEnabled(False)

        self.use_btn = QPushButton("Use This Password")
        self.use_btn.setStyleSheet("""
            QPushButton {
                background-color: #00AA00;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #008800;
            }
        """)
        self.use_btn.clicked.connect(self.accept)
        self.use_btn.setEnabled(False)

        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #AA0000;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #880000;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.copy_btn)
        button_layout.addWidget(self.use_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)

        # Generate initial password
        self.generate_password()

    def generate_password(self):
        """Generate a new password"""
        length = self.length_spin.value()
        use_uppercase = self.uppercase_check.isChecked()
        use_lowercase = self.lowercase_check.isChecked()
        use_numbers = self.numbers_check.isChecked()
        use_symbols = self.symbols_check.isChecked()
        exclude_ambiguous = self.exclude_ambiguous_check.isChecked()

        if not any([use_uppercase, use_lowercase, use_numbers, use_symbols]):
            QMessageBox.warning(self, "Warning", "Please select at least one character type!")
            return

        self.generated_password = PasswordGenerator.generate_password(
            length, use_uppercase, use_lowercase, use_numbers, use_symbols, exclude_ambiguous
        )

        self.password_display.setText(self.generated_password)
        self.copy_btn.setEnabled(True)
        self.use_btn.setEnabled(True)

        # Update strength indicator
        strength_info = PasswordGenerator.check_password_strength(self.generated_password)
        self.strength_label.setText(f"Password Strength: {strength_info['strength']}")
        self.strength_progress.setValue(strength_info['score'])

        self.strength_progress.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid #444;
                border-radius: 5px;
                text-align: center;
                background-color: #2D2D3F;
                color: #FFFFFF;
            }}
            QProgressBar::chunk {{
                background-color: {strength_info['color']};
                border-radius: 5px;
            }}
        """)

    def copy_to_clipboard(self):
        """Copy password to clipboard"""
        from PyQt5.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        clipboard.setText(self.generated_password)
        QMessageBox.information(self, "Copied", "Password copied to clipboard!")

class PasswordManagerWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = DatabaseManager()
        self.setup_ui()
        self.load_passwords()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Title
        title_label = QLabel("Password Manager")
        title_label.setStyleSheet("color: #FFFFFF; font-size: 24px; font-weight: bold;")
        layout.addWidget(title_label)

        # Toolbar
        toolbar_layout = QHBoxLayout()

        self.add_btn = QPushButton("Add Password")
        self.add_btn.setStyleSheet("""
            QPushButton {
                background-color: #8E79E8;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7A67C7;
            }
        """)
        self.add_btn.clicked.connect(self.add_password)

        self.edit_btn = QPushButton("Edit")
        self.edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #3D3D4F;
            }
        """)
        self.edit_btn.clicked.connect(self.edit_password)
        self.edit_btn.setEnabled(False)

        self.delete_btn = QPushButton("Delete")
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #AA0000;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #880000;
            }
        """)
        self.delete_btn.clicked.connect(self.delete_password)
        self.delete_btn.setEnabled(False)

        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search passwords...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        self.search_edit.textChanged.connect(self.filter_passwords)

        toolbar_layout.addWidget(self.add_btn)
        toolbar_layout.addWidget(self.edit_btn)
        toolbar_layout.addWidget(self.delete_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(QLabel("Search:"))
        toolbar_layout.addWidget(self.search_edit)

        layout.addLayout(toolbar_layout)

        # Password table
        self.password_table = QTableWidget()
        self.password_table.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A28;
                color: #FFFFFF;
                border: none;
                gridline-color: #333344;
            }
            QHeaderView::section {
                background-color: #1A1A28;
                color: #AAAAAA;
                border: none;
                padding: 8px;
                font-weight: bold;
            }
            QTableWidget::item {
                border-bottom: 1px solid #333344;
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #2D2D3F;
            }
        """)

        self.password_table.setColumnCount(6)
        self.password_table.setHorizontalHeaderLabels(["Title", "Username", "URL", "Category", "Created", "Modified"])

        # Set column widths
        header = self.password_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Title
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Username
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # URL
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Category
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Created
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Modified

        self.password_table.verticalHeader().setVisible(False)
        self.password_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.password_table.setSelectionMode(QTableWidget.SingleSelection)
        self.password_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.password_table.itemDoubleClicked.connect(self.edit_password)

        layout.addWidget(self.password_table)

    def load_passwords(self):
        """Load passwords from database"""
        passwords = self.db.get_passwords()
        self.password_table.setRowCount(len(passwords))

        for row, password in enumerate(passwords):
            # password = (id, title, username, password, url, notes, created_date, category)
            self.password_table.setItem(row, 0, QTableWidgetItem(password[1]))  # Title
            self.password_table.setItem(row, 1, QTableWidgetItem(password[2] or ""))  # Username
            self.password_table.setItem(row, 2, QTableWidgetItem(password[4] or ""))  # URL
            self.password_table.setItem(row, 3, QTableWidgetItem(password[7] if len(password) > 7 else "General"))  # Category
            self.password_table.setItem(row, 4, QTableWidgetItem(password[6][:10] if password[6] else ""))  # Created date
            self.password_table.setItem(row, 5, QTableWidgetItem(password[6][:10] if password[6] else ""))  # Modified date

            # Store password ID in first column
            self.password_table.item(row, 0).setData(Qt.UserRole, password[0])

    def on_selection_changed(self):
        """Handle selection change"""
        has_selection = len(self.password_table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def add_password(self):
        """Add new password"""
        dialog = PasswordDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_password_data()
            if data['title'] and data['password']:
                self.db.insert_password(
                    data['title'], data['username'], data['password'],
                    data['url'], data['notes'], data['category']
                )
                self.load_passwords()
                QMessageBox.information(self, "Success", "Password added successfully!")
            else:
                QMessageBox.warning(self, "Warning", "Title and password are required!")

    def edit_password(self):
        """Edit selected password"""
        current_row = self.password_table.currentRow()
        if current_row >= 0:
            password_id = self.password_table.item(current_row, 0).data(Qt.UserRole)
            # Get full password data
            passwords = self.db.get_passwords()
            password_data = next((p for p in passwords if p[0] == password_id), None)

            if password_data:
                dialog = PasswordDialog(self, password_data)
                if dialog.exec_() == QDialog.Accepted:
                    data = dialog.get_password_data()
                    if data['title'] and data['password']:
                        # Update password in database
                        query = '''
                            UPDATE passwords
                            SET title=?, username=?, password=?, url=?, notes=?, category=?, modified_date=?
                            WHERE id=?
                        '''
                        encrypted_password = self.db.encrypt_data(data['password'])
                        self.db.execute_query(query, (
                            data['title'], data['username'], encrypted_password,
                            data['url'], data['notes'], data['category'],
                            datetime.now().isoformat(), password_id
                        ))
                        self.load_passwords()
                        QMessageBox.information(self, "Success", "Password updated successfully!")
                    else:
                        QMessageBox.warning(self, "Warning", "Title and password are required!")

    def delete_password(self):
        """Delete selected password"""
        current_row = self.password_table.currentRow()
        if current_row >= 0:
            password_id = self.password_table.item(current_row, 0).data(Qt.UserRole)
            title = self.password_table.item(current_row, 0).text()

            reply = QMessageBox.question(
                self, "Confirm Delete",
                f"Are you sure you want to delete the password for '{title}'?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                query = "DELETE FROM passwords WHERE id=?"
                self.db.execute_query(query, (password_id,))
                self.load_passwords()
                QMessageBox.information(self, "Success", "Password deleted successfully!")

    def filter_passwords(self):
        """Filter passwords based on search text"""
        search_text = self.search_edit.text().lower()

        for row in range(self.password_table.rowCount()):
            show_row = False
            for col in range(self.password_table.columnCount()):
                item = self.password_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break

            self.password_table.setRowHidden(row, not show_row)
