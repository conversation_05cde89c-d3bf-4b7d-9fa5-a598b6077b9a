import os
import json
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QFrame, 
                            QCheckBox, QLineEdit, QPushButton, QComboBox, QTabWidget,
                            QScrollArea, QGroupBox, QFormLayout, QSpinBox, QFileDialog,
                            QRadioButton, QProgressBar, QTextEdit, QListWidget, QListWidgetItem)
from PyQt5.QtGui import QColor, QPixmap, QIcon, QFont
from PyQt5.QtCore import Qt, QTimer

class BulkDestroyWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
        # For simulation purposes
        self.destroy_progress = 0
        self.destroy_timer = QTimer()
        self.destroy_timer.timeout.connect(self.update_destroy_progress)
    
    def setup_ui(self):
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Add title with educational disclaimer
        title_layout = QHBoxLayout()
        
        title_label = QLabel("Bulk Destroy")
        title_label.setStyleSheet("color: #FFFFFF; font-size: 24px; font-weight: bold;")
        
        disclaimer_label = QLabel("(Educational Simulation)")
        disclaimer_label.setStyleSheet("color: #FF5555; font-size: 16px; font-style: italic;")
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(disclaimer_label)
        title_layout.addStretch()
        
        main_layout.addLayout(title_layout)
        
        # Add educational notice
        notice_frame = QFrame()
        notice_frame.setStyleSheet("background-color: #2D2D3F; border-radius: 5px;")
        notice_layout = QVBoxLayout(notice_frame)
        
        notice_title = QLabel("⚠️ Educational Notice")
        notice_title.setStyleSheet("color: #FFAA00; font-size: 16px; font-weight: bold;")
        
        notice_text = QLabel(
            "This is a simulated interface for educational purposes only. "
            "It demonstrates how secure file deletion might be implemented, to help understand "
            "data security concepts. No actual files are deleted or modified."
        )
        notice_text.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        notice_text.setWordWrap(True)
        
        notice_layout.addWidget(notice_title)
        notice_layout.addWidget(notice_text)
        
        main_layout.addWidget(notice_frame)
        
        # Create main content area with two columns
        content_layout = QHBoxLayout()
        
        # Left column - File selection and options
        left_column = QVBoxLayout()
        
        # File selection
        file_frame = QFrame()
        file_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        file_layout = QVBoxLayout(file_frame)
        
        file_title = QLabel("Select Files to Destroy")
        file_title.setStyleSheet("color: #FFFFFF; font-size: 16px; font-weight: bold;")
        file_layout.addWidget(file_title)
        
        # File list
        self.file_list = QListWidget()
        self.file_list.setStyleSheet("""
            QListWidget {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                font-size: 14px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333344;
            }
            QListWidget::item:selected {
                background-color: #3D3D4F;
            }
            QListWidget::item:hover {
                background-color: #353545;
            }
        """)
        self.file_list.setMinimumHeight(200)
        file_layout.addWidget(self.file_list)
        
        # File selection buttons
        file_buttons_layout = QHBoxLayout()
        
        add_file_button = QPushButton("Add Files")
        add_file_button.setStyleSheet("""
            QPushButton {
                background-color: #3D3D4F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #4D4D5F;
            }
        """)
        add_file_button.clicked.connect(self.add_files)
        
        add_folder_button = QPushButton("Add Folder")
        add_folder_button.setStyleSheet("""
            QPushButton {
                background-color: #3D3D4F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #4D4D5F;
            }
        """)
        add_folder_button.clicked.connect(self.add_folder)
        
        remove_button = QPushButton("Remove")
        remove_button.setStyleSheet("""
            QPushButton {
                background-color: #3D3D4F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #4D4D5F;
            }
        """)
        remove_button.clicked.connect(self.remove_files)
        
        clear_button = QPushButton("Clear All")
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #3D3D4F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #4D4D5F;
            }
        """)
        clear_button.clicked.connect(self.clear_files)
        
        file_buttons_layout.addWidget(add_file_button)
        file_buttons_layout.addWidget(add_folder_button)
        file_buttons_layout.addWidget(remove_button)
        file_buttons_layout.addWidget(clear_button)
        
        file_layout.addLayout(file_buttons_layout)
        
        left_column.addWidget(file_frame)
        
        # Destruction options
        options_frame = QFrame()
        options_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        options_layout = QVBoxLayout(options_frame)
        
        options_title = QLabel("Destruction Options")
        options_title.setStyleSheet("color: #FFFFFF; font-size: 16px; font-weight: bold;")
        options_layout.addWidget(options_title)
        
        # Destruction method
        method_group = QGroupBox("Destruction Method")
        method_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        method_layout = QVBoxLayout(method_group)
        
        # Radio button style
        radio_style = """
            QRadioButton {
                color: #FFFFFF;
                font-size: 14px;
                spacing: 10px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                background-color: #2D2D3F;
            }
            QRadioButton::indicator:checked {
                background-color: #8E79E8;
                border: 4px solid #2D2D3F;
            }
            QRadioButton::indicator:unchecked:hover {
                background-color: #3D3D4F;
            }
        """
        
        # Destruction methods
        methods = [
            {"name": "Quick Delete (Recycle Bin)", "checked": False},
            {"name": "Standard Wipe (1 pass)", "checked": True},
            {"name": "DoD 5220.22-M (3 passes)", "checked": False},
            {"name": "Gutmann Method (35 passes)", "checked": False}
        ]
        
        for method in methods:
            radio = QRadioButton(method["name"])
            radio.setStyleSheet(radio_style)
            radio.setChecked(method["checked"])
            method_layout.addWidget(radio)
        
        options_layout.addWidget(method_group)
        
        # Additional options
        additional_group = QGroupBox("Additional Options")
        additional_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #333344;
                border-radius: 5px;
                margin-top: 15px;
                font-size: 14px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        additional_layout = QVBoxLayout(additional_group)
        
        # Checkbox style
        checkbox_style = """
            QCheckBox {
                color: #FFFFFF;
                font-size: 14px;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                background-color: #2D2D3F;
            }
            QCheckBox::indicator:checked {
                background-color: #8E79E8;
                image: url('checkmark.png');
            }
            QCheckBox::indicator:unchecked:hover {
                background-color: #3D3D4F;
            }
        """
        
        # Additional options
        additional_options = [
            {"name": "Remove File Names from MFT", "checked": True},
            {"name": "Clear File System Journal", "checked": False},
            {"name": "Remove from Windows Registry", "checked": True},
            {"name": "Delete Associated Thumbnails", "checked": True}
        ]
        
        for option in additional_options:
            checkbox = QCheckBox(option["name"])
            checkbox.setStyleSheet(checkbox_style)
            checkbox.setChecked(option["checked"])
            additional_layout.addWidget(checkbox)
        
        options_layout.addWidget(additional_group)
        
        left_column.addWidget(options_frame)
        
        # Right column - Destruction log and button
        right_column = QVBoxLayout()
        
        # Destruction log
        log_frame = QFrame()
        log_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        log_layout = QVBoxLayout(log_frame)
        
        log_title = QLabel("Destruction Log (Educational Simulation)")
        log_title.setStyleSheet("color: #FFFFFF; font-size: 16px; font-weight: bold;")
        log_layout.addWidget(log_title)
        
        self.log_text = QTextEdit()
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2D2D3F;
                color: #AAAAAA;
                border: none;
                border-radius: 5px;
                font-family: monospace;
                font-size: 12px;
            }
        """)
        self.log_text.setReadOnly(True)
        self.log_text.setMinimumHeight(300)
        log_layout.addWidget(self.log_text)
        
        right_column.addWidget(log_frame)
        
        # Destruction button and progress
        destroy_frame = QFrame()
        destroy_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        destroy_layout = QVBoxLayout(destroy_frame)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                background-color: #2D2D3F;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #8E79E8;
                border-radius: 5px;
            }
        """)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        
        # Destroy button
        destroy_button = QPushButton("Destroy Files (Educational Simulation)")
        destroy_button.setStyleSheet("""
            QPushButton {
                background-color: #FF5555;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 12px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FF3333;
            }
        """)
        destroy_button.clicked.connect(self.start_destroy_simulation)
        
        destroy_layout.addWidget(self.progress_bar)
        destroy_layout.addWidget(destroy_button)
        
        right_column.addWidget(destroy_frame)
        
        # Add columns to content layout
        content_layout.addLayout(left_column)
        content_layout.addLayout(right_column)
        
        main_layout.addLayout(content_layout)
        
        # Add educational information at the bottom
        info_frame = QFrame()
        info_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        info_layout = QVBoxLayout(info_frame)
        
        info_title = QLabel("🔒 Secure Deletion Information")
        info_title.setStyleSheet("color: #8E79E8; font-size: 16px; font-weight: bold;")
        
        info_text = QLabel(
            "Understanding secure deletion methods is important for data protection. Key concepts:\n"
            "• Standard deletion only removes file references, not the actual data\n"
            "• Secure wiping overwrites file data with random patterns\n"
            "• Multiple passes make data recovery more difficult\n"
            "• For sensitive data, physical destruction of storage media may be necessary\n"
            "• Full disk encryption provides additional protection for all files\n"
            "• Some storage types (SSDs, flash drives) may require specialized wiping methods"
        )
        info_text.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        info_text.setWordWrap(True)
        
        info_layout.addWidget(info_title)
        info_layout.addWidget(info_text)
        
        main_layout.addWidget(info_frame)
        
        # Add some initial log text
        self.log_text.append("[*] Bulk Destroy module initialized (Educational Simulation)")
        self.log_text.append("[*] This is a simulated interface for educational purposes only")
        self.log_text.append("[*] No actual files will be deleted or modified")
        self.log_text.append("[*] Add files or folders to the list to begin the simulation")
    
    def add_files(self):
        # Simulate adding files
        files, _ = QFileDialog.getOpenFileNames(
            self, "Select Files to Destroy", "", "All Files (*)"
        )
        
        for file_path in files:
            item = QListWidgetItem(file_path)
            self.file_list.addItem(item)
        
        self.log_text.append(f"[+] Added {len(files)} file(s) to the destruction list (simulation)")
    
    def add_folder(self):
        # Simulate adding a folder
        folder = QFileDialog.getExistingDirectory(
            self, "Select Folder to Destroy", ""
        )
        
        if folder:
            item = QListWidgetItem(f"{folder} (and all contents)")
            self.file_list.addItem(item)
            
            self.log_text.append(f"[+] Added folder '{folder}' to the destruction list (simulation)")
    
    def remove_files(self):
        # Remove selected files from the list
        selected_items = self.file_list.selectedItems()
        
        for item in selected_items:
            self.file_list.takeItem(self.file_list.row(item))
        
        self.log_text.append(f"[-] Removed {len(selected_items)} item(s) from the destruction list")
    
    def clear_files(self):
        # Clear all files from the list
        count = self.file_list.count()
        self.file_list.clear()
        
        self.log_text.append(f"[-] Cleared all {count} item(s) from the destruction list")
    
    def start_destroy_simulation(self):
        # Check if there are files to destroy
        if self.file_list.count() == 0:
            self.log_text.append("[!] Error: No files selected for destruction")
            return
        
        # Reset progress
        self.destroy_progress = 0
        self.progress_bar.setValue(0)
        
        # Add initial log message
        self.log_text.append("\n[*] Starting educational destruction simulation...")
        self.log_text.append("[*] This is a simulated process for educational purposes only.")
        self.log_text.append("[*] No actual files will be deleted or modified.")
        
        # Start the timer for simulated destruction progress
        self.destroy_timer.start(100)
    
    def update_destroy_progress(self):
        self.destroy_progress += 1
        self.progress_bar.setValue(self.destroy_progress)
        
        # Get total number of files
        total_files = self.file_list.count()
        
        # Add log messages at specific points
        if self.destroy_progress == 10:
            self.log_text.append("[+] Initializing secure destruction process...")
        elif self.destroy_progress == 20:
            self.log_text.append(f"[+] Analyzing {total_files} file(s) for secure destruction...")
        elif self.destroy_progress == 30:
            self.log_text.append("[+] Preparing destruction algorithm...")
            
            # Add simulated file processing
            if total_files > 0:
                file_index = 0
                file_item = self.file_list.item(file_index)
                self.log_text.append(f"[+] Processing: {file_item.text()}")
        
        elif self.destroy_progress == 40:
            self.log_text.append("[+] First wiping pass in progress (writing zeros)...")
            
            # Process next file if available
            if total_files > 1:
                file_index = 1
                file_item = self.file_list.item(file_index)
                self.log_text.append(f"[+] Processing: {file_item.text()}")
                
        elif self.destroy_progress == 50:
            self.log_text.append("[+] Second wiping pass in progress (writing ones)...")
            
            # Process next file if available
            if total_files > 2:
                file_index = 2
                file_item = self.file_list.item(file_index)
                self.log_text.append(f"[+] Processing: {file_item.text()}")
                
        elif self.destroy_progress == 60:
            self.log_text.append("[+] Third wiping pass in progress (writing random patterns)...")
            
            # Process next file if available
            if total_files > 3:
                file_index = 3
                file_item = self.file_list.item(file_index)
                self.log_text.append(f"[+] Processing: {file_item.text()}")
                
        elif self.destroy_progress == 70:
            self.log_text.append("[+] Removing file entries from Master File Table (simulation)...")
        elif self.destroy_progress == 80:
            self.log_text.append("[+] Clearing file system journal entries (simulation)...")
        elif self.destroy_progress == 90:
            self.log_text.append("[+] Removing associated registry entries (simulation)...")
        elif self.destroy_progress == 100:
            self.log_text.append("[+] Secure destruction process completed successfully (simulation)")
            self.log_text.append(f"[+] {total_files} file(s) securely destroyed (simulation)")
            self.log_text.append("[*] REMINDER: This is only a simulation for educational purposes.")
            self.log_text.append("[*] No actual files were deleted or modified.")
            self.destroy_timer.stop()
            
            # Show a message in the parent window's status bar if available
            if hasattr(self.parent(), "statusBar"):
                self.parent().statusBar().showMessage("Destruction simulation completed (Educational Purposes Only)", 5000)
