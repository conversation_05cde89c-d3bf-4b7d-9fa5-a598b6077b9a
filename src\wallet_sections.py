import os
import json
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QProgressBar
from PyQt5.QtGui import QColor, QPixmap, QIcon
from PyQt5.QtCore import Qt

class WalletSectionsWidget(QWidget):
    def __init__(self, dashboard_components, parent=None):
        super().__init__(parent)
        self.dashboard = dashboard_components
        self.setup_ui()
    
    def setup_ui(self):
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(15)
        
        # Create wallet sections title
        title_label = QLabel("Cryptocurrency Wallets")
        title_label.setStyleSheet("color: #FFFFFF; font-size: 16px; font-weight: bold;")
        main_layout.addWidget(title_label)
        
        # Create wallet panels layout
        wallets_layout = QHBoxLayout()
        wallets_layout.setSpacing(15)
        
        # Wallet types with their icons and colors
        wallet_types = [
            {"name": "Exodus", "icon": "🔷", "color": "#3498db"},
            {"name": "Blockchain", "icon": "🔶", "color": "#2980b9"},
            {"name": "Binance", "icon": "🔸", "color": "#f39c12"},
            {"name": "MetaMask", "icon": "🦊", "color": "#e67e22"}
        ]
        
        # Create wallet panels
        self.wallet_panels = {}
        for wallet in wallet_types:
            wallet_panel = self.create_wallet_panel(
                wallet["name"], 
                wallet["icon"], 
                wallet["color"]
            )
            self.wallet_panels[wallet["name"]] = wallet_panel
            wallets_layout.addWidget(wallet_panel)
        
        main_layout.addLayout(wallets_layout)
        
        # Add wallet statistics section
        stats_frame = QFrame()
        stats_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px; margin-top: 10px;")
        stats_layout = QVBoxLayout(stats_frame)
        
        stats_title = QLabel("Wallet Statistics")
        stats_title.setStyleSheet("color: #FFFFFF; font-size: 16px; font-weight: bold;")
        stats_layout.addWidget(stats_title)
        
        # Add wallet progress bars
        self.progress_bars = {}
        for wallet in wallet_types:
            progress_layout = QHBoxLayout()
            
            wallet_name = QLabel(wallet["name"])
            wallet_name.setStyleSheet(f"color: {wallet['color']}; font-size: 14px; font-weight: bold;")
            wallet_name.setFixedWidth(100)
            
            progress_bar = QProgressBar()
            progress_bar.setStyleSheet(f"""
                QProgressBar {{
                    background-color: #2D2D3F;
                    color: #FFFFFF;
                    border-radius: 5px;
                    text-align: center;
                    height: 20px;
                }}
                QProgressBar::chunk {{
                    background-color: {wallet['color']};
                    border-radius: 5px;
                }}
            """)
            progress_bar.setRange(0, 100)
            progress_bar.setValue(0)
            
            value_label = QLabel("0")
            value_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
            value_label.setFixedWidth(50)
            value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            
            progress_layout.addWidget(wallet_name)
            progress_layout.addWidget(progress_bar)
            progress_layout.addWidget(value_label)
            
            self.progress_bars[wallet["name"]] = {
                "bar": progress_bar,
                "label": value_label
            }
            
            stats_layout.addLayout(progress_layout)
        
        main_layout.addWidget(stats_frame)
        
        # Initial update
        self.update_data()
    
    def create_wallet_panel(self, wallet_name, icon, color):
        panel = QFrame()
        panel.setStyleSheet(f"background-color: #1A1A28; border-radius: 5px;")
        panel.setMinimumHeight(120)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Header with icon and name
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"color: {color}; font-size: 24px;")
        
        name_label = QLabel(wallet_name)
        name_label.setStyleSheet(f"color: #FFFFFF; font-size: 16px; font-weight: bold;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(name_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Title
        title_label = QLabel(f"Total {wallet_name} Wallet Log")
        title_label.setStyleSheet("color: #AAAAAA; font-size: 14px;")
        layout.addWidget(title_label)
        
        # Value
        value_label = QLabel("0")
        value_label.setStyleSheet(f"color: {color}; font-size: 24px; font-weight: bold;")
        panel.value_label = value_label  # Store reference for updates
        layout.addWidget(value_label)
        
        # Subtitle
        subtitle_label = QLabel("During all this time")
        subtitle_label.setStyleSheet("color: #AAAAAA; font-size: 12px;")
        layout.addWidget(subtitle_label)
        
        return panel
    
    def update_data(self):
        """Update all wallet data in the UI"""
        total_wallets = sum(self.dashboard.wallet_data.values())
        
        # Update wallet panels
        for wallet, panel in self.wallet_panels.items():
            panel.value_label.setText(str(self.dashboard.wallet_data[wallet]))
        
        # Update progress bars
        for wallet, components in self.progress_bars.items():
            value = self.dashboard.wallet_data[wallet]
            components["label"].setText(str(value))
            
            # Calculate percentage (avoid division by zero)
            if total_wallets > 0:
                percentage = int((value / total_wallets) * 100)
            else:
                percentage = 0
            
            components["bar"].setValue(percentage)
            components["bar"].setFormat(f"{percentage}%")

class CryptocurrencyMonitor(QWidget):
    def __init__(self, dashboard_components, parent=None):
        super().__init__(parent)
        self.dashboard = dashboard_components
        self.setup_ui()
    
    def setup_ui(self):
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(15)
        
        # Create cryptocurrency monitor title
        title_label = QLabel("Cryptocurrency Monitor")
        title_label.setStyleSheet("color: #FFFFFF; font-size: 16px; font-weight: bold;")
        main_layout.addWidget(title_label)
        
        # Create monitor frame
        monitor_frame = QFrame()
        monitor_frame.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        monitor_layout = QVBoxLayout(monitor_frame)
        
        # Add crypto currencies
        currencies = [
            {"name": "Bitcoin", "symbol": "BTC", "icon": "₿", "color": "#f7931a"},
            {"name": "Ethereum", "symbol": "ETH", "icon": "Ξ", "color": "#627eea"},
            {"name": "Binance Coin", "symbol": "BNB", "icon": "BNB", "color": "#f3ba2f"},
            {"name": "Solana", "symbol": "SOL", "icon": "SOL", "color": "#00ffbd"}
        ]
        
        self.currency_values = {}
        
        for currency in currencies:
            currency_layout = QHBoxLayout()
            
            icon_label = QLabel(currency["icon"])
            icon_label.setStyleSheet(f"color: {currency['color']}; font-size: 18px; font-weight: bold;")
            icon_label.setFixedWidth(30)
            
            name_label = QLabel(f"{currency['name']} ({currency['symbol']})")
            name_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
            
            value_label = QLabel("0")
            value_label.setStyleSheet(f"color: {currency['color']}; font-size: 14px; font-weight: bold;")
            value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.currency_values[currency["symbol"]] = value_label
            
            currency_layout.addWidget(icon_label)
            currency_layout.addWidget(name_label)
            currency_layout.addStretch()
            currency_layout.addWidget(value_label)
            
            monitor_layout.addLayout(currency_layout)
        
        main_layout.addWidget(monitor_frame)
        
        # Add note
        note_label = QLabel("Note: This is a simulated cryptocurrency monitor for educational purposes only.")
        note_label.setStyleSheet("color: #AAAAAA; font-size: 12px; font-style: italic;")
        main_layout.addWidget(note_label)
        
        # Simulate some values
        self.simulate_crypto_values()
    
    def simulate_crypto_values(self):
        """Simulate cryptocurrency values for educational purposes"""
        import random
        
        values = {
            "BTC": f"${random.randint(50000, 60000):,}",
            "ETH": f"${random.randint(2500, 3500):,}",
            "BNB": f"${random.randint(400, 600):,}",
            "SOL": f"${random.randint(100, 200):,}"
        }
        
        for symbol, label in self.currency_values.items():
            label.setText(values[symbol])
